@extends('layouts.app')

@section('title', 'Panduan Pengolahan - Pela<PERSON>han Pengolahan Wilkerstat SE2026')

@section('content')
<!-- Page Header -->
<div class="page-header mb-8">
    <div class="page-header-content">
        <div class="page-title-section">
            <h1 class="page-title">
                <i class="fas fa-clipboard-list"></i>
                Panduan Pengolahan
            </h1>
            <p class="page-subtitle">
                Prosedur langkah demi langkah untuk pengolahan data Wilkerstat SE2026 yang akurat dan efisien.
            </p>
        </div>
        <div class="page-actions">
            <button class="btn btn-secondary" onclick="printGuidelines()">
                <i class="fas fa-print"></i>
                Cetak Panduan
            </button>
            <button class="btn btn-primary" onclick="downloadPDF()">
                <i class="fas fa-download"></i>
                Unduh PDF
            </button>
        </div>
    </div>
</div>

<!-- Quick Navigation -->
<div class="quick-nav mb-8">
    <div class="quick-nav-header">
        <h3>Navigasi Cepat</h3>
        <button class="btn btn-outline btn-sm" onclick="toggleQuickNav()">
            <i class="fas fa-chevron-up" id="quickNavToggle"></i>
        </button>
    </div>
    <div class="quick-nav-content" id="quickNavContent">
        <div class="nav-grid">
            @foreach($guidelines as $index => $guideline)
            <a href="#section-{{ $index }}" class="nav-item" onclick="scrollToSection({{ $index }})">
                <div class="nav-icon">
                    <i class="{{ $guideline['icon'] }}"></i>
                </div>
                <div class="nav-content">
                    <div class="nav-title">{{ $guideline['title'] }}</div>
                    <div class="nav-subtitle">{{ count($guideline['steps']) }} langkah</div>
                </div>
            </a>
            @endforeach
        </div>
    </div>
</div>

<!-- Progress Tracker -->
<div class="progress-tracker mb-8">
    <div class="progress-header">
        <h3>Progress Pembelajaran</h3>
        <div class="progress-stats">
            <span class="progress-text">0 dari {{ count($guidelines) }} bagian selesai</span>
            <span class="progress-percentage">0%</span>
        </div>
    </div>
    <div class="progress-bar">
        <div class="progress-fill" id="progressFill"></div>
    </div>
    <div class="progress-sections">
        @foreach($guidelines as $index => $guideline)
        <div class="progress-section" data-section="{{ $index }}">
            <div class="progress-dot" id="dot-{{ $index }}"></div>
            <div class="progress-label">{{ $guideline['title'] }}</div>
        </div>
        @endforeach
    </div>
</div>

<!-- Guidelines Content -->
<div class="guidelines-content">
    @foreach($guidelines as $index => $guideline)
    <div class="guideline-section" id="section-{{ $index }}" data-section="{{ $index }}">
        <div class="section-header">
            <div class="section-number">{{ $index + 1 }}</div>
            <div class="section-title-content">
                <h2 class="section-title">
                    <i class="{{ $guideline['icon'] }}"></i>
                    {{ $guideline['title'] }}
                </h2>
                <p class="section-description">{{ $guideline['description'] }}</p>
                <div class="section-meta">
                    <div class="meta-item">
                        <i class="fas fa-clock"></i>
                        <span>Estimasi: {{ $guideline['duration'] }}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-signal"></i>
                        <span>Tingkat: {{ $guideline['difficulty'] }}</span>
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-list"></i>
                        <span>{{ count($guideline['steps']) }} langkah</span>
                    </div>
                </div>
            </div>
            <div class="section-actions">
                <button class="btn btn-outline btn-sm" onclick="toggleSection({{ $index }})">
                    <i class="fas fa-chevron-down" id="toggle-{{ $index }}"></i>
                </button>
                <button class="btn btn-success btn-sm" onclick="markComplete({{ $index }})" id="complete-{{ $index }}">
                    <i class="fas fa-check"></i>
                    Selesai
                </button>
            </div>
        </div>
        
        <div class="section-content" id="content-{{ $index }}">
            <!-- Prerequisites -->
            @if(isset($guideline['prerequisites']) && count($guideline['prerequisites']) > 0)
            <div class="prerequisites-section mb-6">
                <h4 class="subsection-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    Prasyarat
                </h4>
                <div class="prerequisites-list">
                    @foreach($guideline['prerequisites'] as $prerequisite)
                    <div class="prerequisite-item">
                        <i class="fas fa-check-circle"></i>
                        <span>{{ $prerequisite }}</span>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
            
            <!-- Steps -->
            <div class="steps-section">
                <h4 class="subsection-title">
                    <i class="fas fa-list-ol"></i>
                    Langkah-langkah
                </h4>
                <div class="steps-container">
                    @foreach($guideline['steps'] as $stepIndex => $step)
                    <div class="step-item" data-step="{{ $stepIndex }}">
                        <div class="step-header">
                            <div class="step-number">{{ $stepIndex + 1 }}</div>
                            <div class="step-title-content">
                                <h5 class="step-title">{{ $step['title'] }}</h5>
                                <p class="step-description">{{ $step['description'] }}</p>
                            </div>
                            <div class="step-actions">
                                <button class="btn-step-complete" onclick="toggleStepComplete({{ $index }}, {{ $stepIndex }})" id="step-{{ $index }}-{{ $stepIndex }}">
                                    <i class="fas fa-check"></i>
                                </button>
                            </div>
                        </div>
                        
                        @if(isset($step['details']) && count($step['details']) > 0)
                        <div class="step-details">
                            @foreach($step['details'] as $detail)
                            <div class="detail-item">
                                <i class="fas fa-arrow-right"></i>
                                <span>{{ $detail }}</span>
                            </div>
                            @endforeach
                        </div>
                        @endif
                        
                        @if(isset($step['code']))
                        <div class="step-code">
                            <div class="code-header">
                                <span class="code-label">Contoh Kode/Formula:</span>
                                <button class="btn-copy" onclick="copyCode(this)">
                                    <i class="fas fa-copy"></i>
                                    Salin
                                </button>
                            </div>
                            <pre class="code-block"><code>{{ $step['code'] }}</code></pre>
                        </div>
                        @endif
                        
                        @if(isset($step['warning']))
                        <div class="step-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>{{ $step['warning'] }}</span>
                        </div>
                        @endif
                        
                        @if(isset($step['tip']))
                        <div class="step-tip">
                            <i class="fas fa-lightbulb"></i>
                            <span>{{ $step['tip'] }}</span>
                        </div>
                        @endif
                    </div>
                    @endforeach
                </div>
            </div>
            
            <!-- Common Issues -->
            @if(isset($guideline['common_issues']) && count($guideline['common_issues']) > 0)
            <div class="issues-section mt-6">
                <h4 class="subsection-title">
                    <i class="fas fa-bug"></i>
                    Masalah Umum & Solusi
                </h4>
                <div class="issues-list">
                    @foreach($guideline['common_issues'] as $issue)
                    <div class="issue-item">
                        <div class="issue-problem">
                            <i class="fas fa-times-circle"></i>
                            <strong>Masalah:</strong> {{ $issue['problem'] }}
                        </div>
                        <div class="issue-solution">
                            <i class="fas fa-check-circle"></i>
                            <strong>Solusi:</strong> {{ $issue['solution'] }}
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
            
            <!-- Related Resources -->
            @if(isset($guideline['resources']) && count($guideline['resources']) > 0)
            <div class="resources-section mt-6">
                <h4 class="subsection-title">
                    <i class="fas fa-external-link-alt"></i>
                    Sumber Daya Terkait
                </h4>
                <div class="resources-grid">
                    @foreach($guideline['resources'] as $resource)
                    <a href="{{ $resource['link'] }}" class="resource-card" target="_blank">
                        <div class="resource-icon">
                            <i class="{{ $resource['icon'] }}"></i>
                        </div>
                        <div class="resource-content">
                            <div class="resource-title">{{ $resource['title'] }}</div>
                            <div class="resource-type">{{ $resource['type'] }}</div>
                        </div>
                        <div class="resource-arrow">
                            <i class="fas fa-arrow-right"></i>
                        </div>
                    </a>
                    @endforeach
                </div>
            </div>
            @endif
        </div>
    </div>
    @endforeach
</div>

<!-- Floating Action Button -->
<div class="floating-actions">
    <div class="fab-menu" id="fabMenu">
        <button class="fab fab-main" onclick="toggleFabMenu()">
            <i class="fas fa-plus" id="fabIcon"></i>
        </button>
        <div class="fab-options" id="fabOptions">
            <button class="fab fab-option" onclick="scrollToTop()" title="Ke Atas">
                <i class="fas fa-arrow-up"></i>
            </button>
            <button class="fab fab-option" onclick="toggleDarkMode()" title="Mode Gelap">
                <i class="fas fa-moon"></i>
            </button>
            <button class="fab fab-option" onclick="increaseFontSize()" title="Perbesar Teks">
                <i class="fas fa-plus"></i>
            </button>
            <button class="fab fab-option" onclick="decreaseFontSize()" title="Perkecil Teks">
                <i class="fas fa-minus"></i>
            </button>
        </div>
    </div>
</div>

<!-- Completion Modal -->
<div class="modal" id="completionModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">
                <i class="fas fa-trophy"></i>
                Selamat!
            </h3>
        </div>
        <div class="modal-body">
            <div class="completion-content">
                <div class="completion-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h4>Anda telah menyelesaikan semua panduan!</h4>
                <p>Semua langkah pengolahan Wilkerstat SE2026 telah dipelajari dengan baik.</p>
                <div class="completion-stats">
                    <div class="stat-item">
                        <div class="stat-number">{{ count($guidelines) }}</div>
                        <div class="stat-label">Bagian Selesai</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">Progress</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeCompletionModal()">Tutup</button>
            <button class="btn btn-primary" onclick="resetProgress()">Mulai Ulang</button>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    /* Page Header */
    .page-header {
        background: linear-gradient(135deg, var(--bg-white) 0%, #f8fafc 100%);
        border-radius: 0.75rem;
        padding: 2rem;
        border: 1px solid var(--border-color);
    }

    .page-header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 2rem;
    }

    .page-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .page-title i {
        color: var(--primary-color);
    }

    .page-subtitle {
        color: var(--text-secondary);
        font-size: 1.125rem;
        line-height: 1.6;
    }

    .page-actions {
        display: flex;
        gap: 0.75rem;
    }

    /* Quick Navigation */
    .quick-nav {
        background: var(--bg-white);
        border-radius: 0.75rem;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
        overflow: hidden;
    }

    .quick-nav-header {
        padding: 1.5rem;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .quick-nav-header h3 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
    }

    .quick-nav-content {
        padding: 1.5rem;
        transition: all 0.3s ease;
    }

    .quick-nav-content.collapsed {
        display: none;
    }

    .nav-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .nav-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        text-decoration: none;
        color: var(--text-primary);
        transition: all 0.3s ease;
        background: var(--bg-white);
    }

    .nav-item:hover {
        border-color: var(--primary-color);
        box-shadow: var(--shadow-sm);
        transform: translateY(-2px);
    }

    .nav-icon {
        width: 40px;
        height: 40px;
        border-radius: 0.5rem;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
    }

    .nav-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .nav-subtitle {
        font-size: 0.875rem;
        color: var(--text-secondary);
    }

    /* Progress Tracker */
    .progress-tracker {
        background: var(--bg-white);
        border-radius: 0.75rem;
        padding: 1.5rem;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
    }

    .progress-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1.5rem;
    }

    .progress-header h3 {
        margin: 0;
        color: var(--text-primary);
    }

    .progress-stats {
        display: flex;
        gap: 1rem;
        align-items: center;
    }

    .progress-text {
        color: var(--text-secondary);
        font-size: 0.875rem;
    }

    .progress-percentage {
        background: var(--primary-color);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
        font-size: 0.875rem;
        font-weight: 600;
    }

    .progress-bar {
        height: 8px;
        background: var(--bg-light);
        border-radius: 9999px;
        overflow: hidden;
        margin-bottom: 1rem;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
        border-radius: 9999px;
        width: 0%;
        transition: width 0.5s ease;
    }

    .progress-sections {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .progress-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
        flex: 1;
    }

    .progress-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: var(--bg-light);
        border: 2px solid var(--border-color);
        transition: all 0.3s ease;
    }

    .progress-dot.completed {
        background: var(--primary-color);
        border-color: var(--primary-color);
    }

    .progress-label {
        font-size: 0.75rem;
        color: var(--text-secondary);
        text-align: center;
        max-width: 80px;
        line-height: 1.2;
    }

    /* Guideline Sections */
    .guideline-section {
        background: var(--bg-white);
        border-radius: 0.75rem;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
        margin-bottom: 2rem;
        overflow: hidden;
    }

    .section-header {
        padding: 2rem;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-bottom: 1px solid var(--border-color);
        display: flex;
        align-items: flex-start;
        gap: 1.5rem;
    }

    .section-number {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: 700;
        flex-shrink: 0;
    }

    .section-title-content {
        flex: 1;
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .section-title i {
        color: var(--primary-color);
    }

    .section-description {
        color: var(--text-secondary);
        line-height: 1.6;
        margin-bottom: 1rem;
    }

    .section-meta {
        display: flex;
        gap: 1.5rem;
        flex-wrap: wrap;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: var(--text-secondary);
    }

    .meta-item i {
        color: var(--primary-color);
    }

    .section-actions {
        display: flex;
        gap: 0.5rem;
        align-items: flex-start;
    }

    .section-content {
        padding: 2rem;
        transition: all 0.3s ease;
    }

    .section-content.collapsed {
        display: none;
    }

    /* Subsections */
    .subsection-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .subsection-title i {
        color: var(--primary-color);
    }

    /* Prerequisites */
    .prerequisites-list {
        display: grid;
        gap: 0.75rem;
    }

    .prerequisite-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 0.5rem;
        color: #0c4a6e;
    }

    .prerequisite-item i {
        color: #0284c7;
    }

    /* Steps */
    .steps-container {
        display: grid;
        gap: 1.5rem;
    }

    .step-item {
        border: 1px solid var(--border-color);
        border-radius: 0.75rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .step-item.completed {
        border-color: #10b981;
        background: #f0fdf4;
    }

    .step-header {
        padding: 1.5rem;
        background: var(--bg-white);
        display: flex;
        align-items: flex-start;
        gap: 1rem;
    }

    .step-item.completed .step-header {
        background: #f0fdf4;
    }

    .step-number {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: var(--bg-light);
        border: 2px solid var(--border-color);
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        color: var(--text-secondary);
        flex-shrink: 0;
        transition: all 0.3s ease;
    }

    .step-item.completed .step-number {
        background: #10b981;
        border-color: #10b981;
        color: white;
    }

    .step-title-content {
        flex: 1;
    }

    .step-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
    }

    .step-description {
        color: var(--text-secondary);
        line-height: 1.6;
    }

    .step-actions {
        display: flex;
        align-items: flex-start;
    }

    .btn-step-complete {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        border: 2px solid var(--border-color);
        background: var(--bg-white);
        color: var(--text-secondary);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .btn-step-complete:hover {
        border-color: #10b981;
        color: #10b981;
    }

    .btn-step-complete.completed {
        background: #10b981;
        border-color: #10b981;
        color: white;
    }

    /* Step Details */
    .step-details {
        padding: 0 1.5rem 1rem 1.5rem;
        margin-left: 50px;
    }

    .detail-item {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        margin-bottom: 0.5rem;
        color: var(--text-secondary);
        font-size: 0.875rem;
        line-height: 1.5;
    }

    .detail-item i {
        color: var(--primary-color);
        margin-top: 0.125rem;
        flex-shrink: 0;
    }

    /* Code Blocks */
    .step-code {
        margin: 1rem 1.5rem;
        margin-left: calc(1.5rem + 50px);
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .code-header {
        padding: 0.75rem 1rem;
        background: #f8fafc;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .code-label {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-primary);
    }

    .btn-copy {
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 0.25rem 0.75rem;
        border-radius: 0.25rem;
        font-size: 0.75rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 0.25rem;
        transition: all 0.3s ease;
    }

    .btn-copy:hover {
        background: var(--primary-dark);
    }

    .code-block {
        margin: 0;
        padding: 1rem;
        background: #1e293b;
        color: #e2e8f0;
        font-family: 'Courier New', monospace;
        font-size: 0.875rem;
        line-height: 1.5;
        overflow-x: auto;
    }

    /* Warnings and Tips */
    .step-warning {
        margin: 1rem 1.5rem;
        margin-left: calc(1.5rem + 50px);
        padding: 1rem;
        background: #fef2f2;
        border: 1px solid #fecaca;
        border-radius: 0.5rem;
        color: #991b1b;
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .step-warning i {
        color: #dc2626;
        margin-top: 0.125rem;
        flex-shrink: 0;
    }

    .step-tip {
        margin: 1rem 1.5rem;
        margin-left: calc(1.5rem + 50px);
        padding: 1rem;
        background: #fffbeb;
        border: 1px solid #fed7aa;
        border-radius: 0.5rem;
        color: #92400e;
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
    }

    .step-tip i {
        color: #f59e0b;
        margin-top: 0.125rem;
        flex-shrink: 0;
    }

    /* Issues Section */
    .issues-list {
        display: grid;
        gap: 1rem;
    }

    .issue-item {
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        overflow: hidden;
    }

    .issue-problem {
        padding: 1rem;
        background: #fef2f2;
        border-bottom: 1px solid #fecaca;
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        color: #991b1b;
    }

    .issue-problem i {
        color: #dc2626;
        margin-top: 0.125rem;
        flex-shrink: 0;
    }

    .issue-solution {
        padding: 1rem;
        background: #f0fdf4;
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        color: #166534;
    }

    .issue-solution i {
        color: #16a34a;
        margin-top: 0.125rem;
        flex-shrink: 0;
    }

    /* Resources Grid */
    .resources-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .resource-card {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        text-decoration: none;
        color: var(--text-primary);
        transition: all 0.3s ease;
        background: var(--bg-white);
    }

    .resource-card:hover {
        border-color: var(--primary-color);
        box-shadow: var(--shadow-sm);
        transform: translateY(-2px);
    }

    .resource-icon {
        width: 40px;
        height: 40px;
        border-radius: 0.5rem;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        flex-shrink: 0;
    }

    .resource-content {
        flex: 1;
    }

    .resource-title {
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .resource-type {
        font-size: 0.875rem;
        color: var(--text-secondary);
    }

    .resource-arrow {
        color: var(--text-secondary);
    }

    /* Floating Actions */
    .floating-actions {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        z-index: 1000;
    }

    .fab-menu {
        position: relative;
    }

    .fab {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        border: none;
        box-shadow: var(--shadow-lg);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        transition: all 0.3s ease;
    }

    .fab-main {
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
    }

    .fab-main:hover {
        transform: scale(1.1);
    }

    .fab-options {
        position: absolute;
        bottom: 70px;
        right: 0;
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        opacity: 0;
        visibility: hidden;
        transform: translateY(20px);
        transition: all 0.3s ease;
    }

    .fab-options.open {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
    }

    .fab-option {
        background: var(--bg-white);
        color: var(--text-primary);
        border: 1px solid var(--border-color);
        width: 48px;
        height: 48px;
        font-size: 1rem;
    }

    .fab-option:hover {
        background: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

    /* Completion Modal */
    .completion-content {
        text-align: center;
        padding: 2rem 0;
    }

    .completion-icon {
        font-size: 4rem;
        color: #10b981;
        margin-bottom: 1rem;
    }

    .completion-content h4 {
        color: var(--text-primary);
        margin-bottom: 0.5rem;
    }

    .completion-content p {
        color: var(--text-secondary);
        margin-bottom: 2rem;
    }

    .completion-stats {
        display: flex;
        justify-content: center;
        gap: 2rem;
    }

    .stat-item {
        text-align: center;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        line-height: 1;
    }

    .stat-label {
        font-size: 0.875rem;
        color: var(--text-secondary);
        margin-top: 0.25rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .page-header-content {
            flex-direction: column;
            align-items: stretch;
        }

        .page-actions {
            justify-content: flex-end;
        }

        .nav-grid {
            grid-template-columns: 1fr;
        }

        .progress-sections {
            flex-wrap: wrap;
            gap: 1rem;
        }

        .section-header {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        .section-actions {
            justify-content: flex-end;
        }

        .section-meta {
            flex-direction: column;
            gap: 0.5rem;
        }

        .step-header {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        .step-actions {
            justify-content: flex-end;
        }

        .step-details,
        .step-code,
        .step-warning,
        .step-tip {
            margin-left: 1.5rem;
        }

        .resources-grid {
            grid-template-columns: 1fr;
        }

        .floating-actions {
            bottom: 1rem;
            right: 1rem;
        }

        .completion-stats {
            flex-direction: column;
            gap: 1rem;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    let completedSections = JSON.parse(localStorage.getItem('guidelineProgress') || '[]');
    let completedSteps = JSON.parse(localStorage.getItem('stepProgress') || '{}');
    let fontSize = parseInt(localStorage.getItem('fontSize') || '16');
    let darkMode = localStorage.getItem('darkMode') === 'true';

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        updateProgress();
        applyFontSize();
        if (darkMode) {
            document.body.classList.add('dark-mode');
        }
        
        // Initialize completed states
        completedSections.forEach(sectionIndex => {
            markSectionComplete(sectionIndex, false);
        });
        
        Object.keys(completedSteps).forEach(sectionIndex => {
            completedSteps[sectionIndex].forEach(stepIndex => {
                markStepComplete(sectionIndex, stepIndex, false);
            });
        });
    });

    // Scroll to section
    function scrollToSection(index) {
        const section = document.getElementById(`section-${index}`);
        section.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    // Toggle quick navigation
    function toggleQuickNav() {
        const content = document.getElementById('quickNavContent');
        const icon = document.getElementById('quickNavToggle');
        
        content.classList.toggle('collapsed');
        if (content.classList.contains('collapsed')) {
            icon.className = 'fas fa-chevron-down';
        } else {
            icon.className = 'fas fa-chevron-up';
        }
    }

    // Toggle section
    function toggleSection(index) {
        const content = document.getElementById(`content-${index}`);
        const icon = document.getElementById(`toggle-${index}`);
        
        content.classList.toggle('collapsed');
        icon.classList.toggle('rotated');
    }

    // Mark section complete
    function markComplete(sectionIndex) {
        if (completedSections.includes(sectionIndex)) {
            // Unmark as complete
            completedSections = completedSections.filter(s => s !== sectionIndex);
            markSectionComplete(sectionIndex, false);
        } else {
            // Mark as complete
            completedSections.push(sectionIndex);
            markSectionComplete(sectionIndex, true);
        }
        
        localStorage.setItem('guidelineProgress', JSON.stringify(completedSections));
        updateProgress();
    }

    // Mark section complete visually
    function markSectionComplete(sectionIndex, completed) {
        const button = document.getElementById(`complete-${sectionIndex}`);
        const dot = document.getElementById(`dot-${sectionIndex}`);
        const section = document.getElementById(`section-${sectionIndex}`);
        
        if (completed) {
            button.innerHTML = '<i class="fas fa-check"></i> Selesai';
            button.classList.add('completed');
            dot.classList.add('completed');
            section.classList.add('completed');
        } else {
            button.innerHTML = '<i class="fas fa-check"></i> Selesai';
            button.classList.remove('completed');
            dot.classList.remove('completed');
            section.classList.remove('completed');
        }
    }

    // Toggle step complete
    function toggleStepComplete(sectionIndex, stepIndex) {
        if (!completedSteps[sectionIndex]) {
            completedSteps[sectionIndex] = [];
        }
        
        if (completedSteps[sectionIndex].includes(stepIndex)) {
            // Unmark step
            completedSteps[sectionIndex] = completedSteps[sectionIndex].filter(s => s !== stepIndex);
            markStepComplete(sectionIndex, stepIndex, false);
        } else {
            // Mark step
            completedSteps[sectionIndex].push(stepIndex);
            markStepComplete(sectionIndex, stepIndex, true);
        }
        
        localStorage.setItem('stepProgress', JSON.stringify(completedSteps));
    }

    // Mark step complete visually
    function markStepComplete(sectionIndex, stepIndex, completed) {
        const button = document.getElementById(`step-${sectionIndex}-${stepIndex}`);
        const stepItem = button.closest('.step-item');
        
        if (completed) {
            button.classList.add('completed');
            stepItem.classList.add('completed');
        } else {
            button.classList.remove('completed');
            stepItem.classList.remove('completed');
        }
    }

    // Update progress
    function updateProgress() {
        const totalSections = {{ count($guidelines) }};
        const completedCount = completedSections.length;
        const percentage = Math.round((completedCount / totalSections) * 100);
        
        document.querySelector('.progress-text').textContent = `${completedCount} dari ${totalSections} bagian selesai`;
        document.querySelector('.progress-percentage').textContent = `${percentage}%`;
        document.getElementById('progressFill').style.width = `${percentage}%`;
        
        // Check if all completed
        if (completedCount === totalSections && totalSections > 0) {
            setTimeout(() => {
                document.getElementById('completionModal').style.display = 'block';
            }, 500);
        }
    }

    // Copy code
    function copyCode(button) {
        const codeBlock = button.closest('.step-code').querySelector('code');
        const text = codeBlock.textContent;
        
        navigator.clipboard.writeText(text).then(() => {
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check"></i> Tersalin';
            setTimeout(() => {
                button.innerHTML = originalText;
            }, 2000);
        });
    }

    // Floating action menu
    function toggleFabMenu() {
        const options = document.getElementById('fabOptions');
        const icon = document.getElementById('fabIcon');
        
        options.classList.toggle('open');
        if (options.classList.contains('open')) {
            icon.className = 'fas fa-times';
        } else {
            icon.className = 'fas fa-plus';
        }
    }

    // Scroll to top
    function scrollToTop() {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }

    // Font size controls
    function increaseFontSize() {
        fontSize = Math.min(fontSize + 2, 24);
        applyFontSize();
        localStorage.setItem('fontSize', fontSize.toString());
    }

    function decreaseFontSize() {
        fontSize = Math.max(fontSize - 2, 12);
        applyFontSize();
        localStorage.setItem('fontSize', fontSize.toString());
    }

    function applyFontSize() {
        document.documentElement.style.fontSize = fontSize + 'px';
    }

    // Dark mode toggle
    function toggleDarkMode() {
        darkMode = !darkMode;
        document.body.classList.toggle('dark-mode');
        localStorage.setItem('darkMode', darkMode.toString());
    }

    // Print guidelines
    function printGuidelines() {
        window.print();
    }

    // Download PDF
    function downloadPDF() {
        alert('Mengunduh panduan dalam format PDF...\nFitur ini akan mengarahkan ke file PDF lengkap.');
    }

    // Reset progress
    function resetProgress() {
        if (confirm('Apakah Anda yakin ingin mengatur ulang semua progress?')) {
            completedSections = [];
            completedSteps = {};
            localStorage.removeItem('guidelineProgress');
            localStorage.removeItem('stepProgress');
            
            // Reset visual states
            document.querySelectorAll('.btn-step-complete').forEach(btn => {
                btn.classList.remove('completed');
            });
            document.querySelectorAll('.step-item').forEach(item => {
                item.classList.remove('completed');
            });
            document.querySelectorAll('.progress-dot').forEach(dot => {
                dot.classList.remove('completed');
            });
            document.querySelectorAll('.guideline-section').forEach(section => {
                section.classList.remove('completed');
            });
            
            updateProgress();
            closeCompletionModal();
        }
    }

    // Close completion modal
    function closeCompletionModal() {
        document.getElementById('completionModal').style.display = 'none';
    }

    // Close modal on outside click
    document.getElementById('completionModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeCompletionModal();
        }
    });
</script>
@endpush