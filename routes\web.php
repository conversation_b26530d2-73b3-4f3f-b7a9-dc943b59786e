<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\TrainingController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Training System Routes
Route::get('/', [TrainingController::class, 'index'])->name('home');
Route::get('/jadwal', [TrainingController::class, 'schedule'])->name('schedule');
Route::get('/materi', [TrainingController::class, 'materials'])->name('materials');
Route::get('/materi/pendahuluan', [TrainingController::class, 'pendahuluan'])->name('pendahuluan');
Route::get('/materi/organisasi-pengolahan', [TrainingController::class, 'organisasiPengolahan'])->name('organisasi-pengolahan');
Route::get('/materi/mekanisme-pengolahan', [TrainingController::class, 'mekanismePengolahan'])->name('materi-mekanisme-pen');
Route::get('/materi/sipw-se2026', [TrainingController::class, 'sipwSe2026'])->name('sipw-se2026');
Route::get('/materi/pengolahan-peta', [TrainingController::class, 'pengolahanPeta'])->name('pengolahan-peta');
Route::get('/panduan', [TrainingController::class, 'guidelines'])->name('guidelines');
Route::get('/sumber-daya', [TrainingController::class, 'resources'])->name('resources');
Route::get('/kontak', [TrainingController::class, 'contact'])->name('contact');
