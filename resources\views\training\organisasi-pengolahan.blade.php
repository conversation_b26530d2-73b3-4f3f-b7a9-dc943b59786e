@extends('layouts.app')

@section('title', $organisasiData['title'])

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">{{ $organisasiData['title'] }}</h1>
                    <p class="page-subtitle">{{ $organisasiData['subtitle'] }}</p>
                    <p class="page-description">{{ $organisasiData['description'] }}</p>
                </div>
                <div class="col-lg-4 text-end">
                    <div class="material-info">
                        <div class="info-item">
                            <i class="fas fa-clock"></i>
                            <span>{{ $organisasiData['duration'] }}</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-signal"></i>
                            <span>{{ $organisasiData['difficulty'] }}</span>
                        </div>
                        <a href="{{ $organisasiData['pdf_link'] }}" class="btn btn-primary" target="_blank">
                            <i class="fas fa-download"></i> Download PDF
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Breadcrumb -->
    <div class="breadcrumb-section">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('home') }}">Beranda</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('materials') }}">Materi</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ $organisasiData['title'] }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Content Sections -->
    <div class="content-sections">
        <div class="container">
            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-9">
                    @foreach($organisasiData['sections'] as $sectionKey => $section)
                    <div class="content-section" id="{{ $sectionKey }}">
                        <div class="section-header">
                            <h2 class="section-title">
                                <i class="{{ $section['icon'] }}"></i>
                                {{ $section['title'] }}
                            </h2>
                        </div>
                        
                        <div class="section-content">
                            @if($sectionKey === 'tim_wilkerstat')
                                <!-- Tim Wilkerstat Content with detailed responsibilities -->
                                <div class="responsibilities-section">
                                    <div class="responsibilities-grid">
                                        @foreach($section['content'] as $index => $responsibility)
                                        <div class="responsibility-item">
                                            <div class="responsibility-number">{{ $index + 1 }}</div>
                                            <div class="responsibility-text">{{ $responsibility }}</div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                                
                            @elseif($sectionKey === 'catatan_tambahan')
                                <!-- Catatan Tambahan Content -->
                                <div class="notes-section">
                                    @foreach($section['content'] as $note)
                                    <div class="note-item">
                                        <i class="fas fa-info-circle"></i>
                                        <span>{{ $note }}</span>
                                    </div>
                                    @endforeach
                                    
                                    @if(isset($section['reference_link']))
                                    <div class="reference-link mt-4">
                                        <a href="{{ $section['reference_link'] }}" class="btn btn-outline-primary">
                                            <i class="fas fa-external-link-alt"></i>
                                            {{ $section['reference_text'] }}
                                        </a>
                                    </div>
                                    @endif
                                </div>
                                
                            @else
                                <!-- Default Content for other sections -->
                                <div class="content-list">
                                    @foreach($section['content'] as $content)
                                    <div class="content-item">
                                        <i class="fas fa-check-circle"></i>
                                        <span>{{ $content }}</span>
                                    </div>
                                    @endforeach
                                </div>
                            @endif
                            
                            @if(isset($section['kesimpulan']))
                            <div class="section-conclusion">
                                <div class="conclusion-box">
                                    <h5><i class="fas fa-lightbulb"></i> Kesimpulan</h5>
                                    <p>{{ $section['kesimpulan'] }}</p>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>
                
                <!-- Sidebar -->
                <div class="col-lg-3">
                    <div class="sidebar">
                        <!-- Table of Contents -->
                        <div class="toc-card">
                            <h4>Daftar Isi</h4>
                            <ul class="toc-list">
                                @foreach($organisasiData['sections'] as $sectionKey => $section)
                                <li>
                                    <a href="#{{ $sectionKey }}">
                                        <i class="{{ $section['icon'] }}"></i>
                                        {{ $section['title'] }}
                                    </a>
                                </li>
                                @endforeach
                            </ul>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="quick-actions-card">
                            <h4>Aksi Cepat</h4>
                            <div class="action-buttons">
                                <a href="{{ $organisasiData['pdf_link'] }}" class="action-btn" target="_blank">
                                    <i class="fas fa-download"></i>
                                    Download PDF
                                </a>
                                <a href="{{ route('materials') }}" class="action-btn">
                                    <i class="fas fa-arrow-left"></i>
                                    Kembali ke Materi
                                </a>
                                <a href="{{ route('schedule') }}" class="action-btn">
                                    <i class="fas fa-calendar-alt"></i>
                                    Lihat Jadwal
                                </a>
                            </div>
                        </div>
                        
                        <!-- Material Info -->
                        <div class="material-info-card">
                            <h4>Informasi Materi</h4>
                            <div class="info-list">
                                <div class="info-row">
                                    <span class="info-label">Durasi:</span>
                                    <span class="info-value">{{ $organisasiData['duration'] }}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Tingkat:</span>
                                    <span class="info-value">{{ $organisasiData['difficulty'] }}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Format:</span>
                                    <span class="info-value">PDF Document</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    /* Page Header */
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 4rem 0;
        margin-bottom: 0;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .page-subtitle {
        font-size: 1.25rem;
        opacity: 0.9;
        margin-bottom: 1rem;
    }

    .page-description {
        font-size: 1.1rem;
        opacity: 0.8;
        line-height: 1.6;
    }

    .material-info {
        text-align: right;
    }

    .info-item {
        display: inline-block;
        margin: 0 1rem 1rem 0;
        padding: 0.5rem 1rem;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 0.5rem;
        backdrop-filter: blur(10px);
    }

    .info-item i {
        margin-right: 0.5rem;
    }

    /* Breadcrumb */
    .breadcrumb-section {
        background: #f8f9fa;
        padding: 1rem 0;
        border-bottom: 1px solid #e9ecef;
    }

    .breadcrumb {
        background: none;
        margin: 0;
        padding: 0;
    }

    .breadcrumb-item a {
        color: #6c757d;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #495057;
    }

    /* Content Sections */
    .content-sections {
        padding: 3rem 0;
    }

    .content-section {
        margin-bottom: 3rem;
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .section-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem 2rem;
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-content {
        padding: 2rem;
    }

    /* Responsibilities Grid */
    .responsibilities-grid {
        display: grid;
        gap: 1rem;
    }

    .responsibility-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 0.5rem;
        border-left: 4px solid #667eea;
    }

    .responsibility-number {
        background: #667eea;
        color: white;
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        flex-shrink: 0;
    }

    .responsibility-text {
        flex: 1;
        line-height: 1.6;
    }

    /* Content List */
    .content-list {
        display: grid;
        gap: 0.75rem;
    }

    .content-item {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 0.75rem;
        background: #f8f9fa;
        border-radius: 0.5rem;
    }

    .content-item i {
        color: #28a745;
        margin-top: 0.25rem;
        flex-shrink: 0;
    }

    /* Notes Section */
    .notes-section {
        display: grid;
        gap: 1rem;
    }

    .note-item {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 1rem;
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 0.5rem;
    }

    .note-item i {
        color: #856404;
        margin-top: 0.25rem;
        flex-shrink: 0;
    }

    /* Conclusion Box */
    .section-conclusion {
        margin-top: 2rem;
    }

    .conclusion-box {
        background: #e8f5e8;
        border: 1px solid #c3e6c3;
        border-radius: 0.5rem;
        padding: 1.5rem;
    }

    .conclusion-box h5 {
        color: #155724;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .conclusion-box p {
        color: #155724;
        margin: 0;
        line-height: 1.6;
    }

    /* Sidebar */
    .sidebar {
        position: sticky;
        top: 2rem;
    }

    .toc-card,
    .quick-actions-card,
    .material-info-card {
        background: white;
        border-radius: 0.75rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 1.5rem;
        overflow: hidden;
    }

    .toc-card h4,
    .quick-actions-card h4,
    .material-info-card h4 {
        background: #667eea;
        color: white;
        padding: 1rem 1.5rem;
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
    }

    .toc-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .toc-list li {
        border-bottom: 1px solid #e9ecef;
    }

    .toc-list li:last-child {
        border-bottom: none;
    }

    .toc-list a {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1.5rem;
        color: #495057;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .toc-list a:hover {
        background: #f8f9fa;
        color: #667eea;
    }

    .action-buttons {
        padding: 1rem 1.5rem;
        display: grid;
        gap: 0.75rem;
    }

    .action-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        background: #f8f9fa;
        color: #495057;
        text-decoration: none;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        border: 1px solid #e9ecef;
    }

    .action-btn:hover {
        background: #667eea;
        color: white;
        transform: translateY(-2px);
    }

    .info-list {
        padding: 1rem 1.5rem;
    }

    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e9ecef;
    }

    .info-row:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
    }

    .info-value {
        color: #6c757d;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .page-header {
            padding: 2rem 0;
        }

        .page-title {
            font-size: 2rem;
        }

        .material-info {
            text-align: left;
            margin-top: 2rem;
        }

        .info-item {
            display: block;
            margin: 0 0 1rem 0;
        }

        .content-sections {
            padding: 2rem 0;
        }

        .section-content {
            padding: 1.5rem;
        }

        .sidebar {
            margin-top: 2rem;
            position: static;
        }

        .responsibility-item {
            flex-direction: column;
            text-align: center;
        }

        .responsibility-number {
            align-self: center;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    // Smooth scrolling for table of contents
    document.querySelectorAll('.toc-list a').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Highlight active section in TOC
    window.addEventListener('scroll', function() {
        const sections = document.querySelectorAll('.content-section');
        const tocLinks = document.querySelectorAll('.toc-list a');
        
        let currentSection = '';
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop - 100;
            const sectionHeight = section.offsetHeight;
            
            if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
                currentSection = section.getAttribute('id');
            }
        });
        
        tocLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === '#' + currentSection) {
                link.classList.add('active');
            }
        });
    });
</script>
@endpush