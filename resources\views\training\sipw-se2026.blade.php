@extends('layouts.app')

@section('title', $sipwData['title'])

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">{{ $sipwData['title'] }}</h1>
                    <p class="page-subtitle">{{ $sipwData['subtitle'] }}</p>
                    <p class="page-description">{{ $sipwData['description'] }}</p>
                </div>
                <div class="col-lg-4 text-end">
                    <div class="material-info">
                        <div class="info-item">
                            <i class="fas fa-clock"></i>
                            <span>{{ $sipwData['duration'] }}</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-signal"></i>
                            <span>{{ $sipwData['difficulty'] }}</span>
                        </div>
                        <a href="{{ $sipwData['pdf_link'] }}" class="btn btn-primary" target="_blank">
                            <i class="fas fa-download"></i> Download PDF
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Breadcrumb -->
    <div class="breadcrumb-section">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('home') }}">Beranda</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('materials') }}">Materi</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ $sipwData['title'] }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Content Sections -->
    <div class="content-sections">
        <div class="container">
            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-9">
                    @foreach($sipwData['sections'] as $sectionKey => $section)
                    <div class="content-section" id="{{ $sectionKey }}">
                        <div class="section-header">
                            <h2 class="section-title">
                                <i class="{{ $section['icon'] }}"></i>
                                {{ $section['title'] }}
                            </h2>
                        </div>
                        <div class="section-content">
                            @if($sectionKey === 'penjelasan_umum')
                                @foreach($section['content'] as $contentKey => $contentItem)
                                    <div class="subsection">
                                        <h3 class="subsection-title">{{ $contentItem['title'] }}</h3>
                                        <ul class="content-list">
                                            @foreach($contentItem['items'] as $item)
                                                <li>{{ $item }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endforeach
                            @elseif($sectionKey === 'tata_cara_pengoperasian')
                                @foreach($section['content'] as $contentKey => $contentItem)
                                    <div class="subsection">
                                        <h3 class="subsection-title">{{ $contentItem['title'] }}</h3>
                                        @if(isset($contentItem['description']))
                                            <p class="subsection-description">{{ $contentItem['description'] }}</p>
                                        @endif
                                        
                                        @if(isset($contentItem['steps']))
                                            <div class="steps-container">
                                                <h4>Langkah-langkah:</h4>
                                                <ol class="steps-list">
                                                    @foreach($contentItem['steps'] as $step)
                                                        <li>{{ $step }}</li>
                                                    @endforeach
                                                </ol>
                                            </div>
                                        @endif
                                        
                                        @if(isset($contentItem['features']))
                                            <div class="features-container">
                                                <h4>Fitur:</h4>
                                                <ul class="features-list">
                                                    @foreach($contentItem['features'] as $feature)
                                                        <li>{{ $feature }}</li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        @endif
                                        
                                        @if(isset($contentItem['roles']))
                                            <div class="roles-container">
                                                <h4>Role Pengguna:</h4>
                                                <ul class="roles-list">
                                                    @foreach($contentItem['roles'] as $role)
                                                        <li>{{ $role }}</li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        @endif
                                        
                                        @if(isset($contentItem['process']))
                                            <div class="process-container">
                                                <h4>Proses:</h4>
                                                <ul class="process-list">
                                                    @foreach($contentItem['process'] as $process)
                                                        <li>{{ $process }}</li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            @elseif($sectionKey === 'tata_cara_entri')
                                @foreach($section['content'] as $contentKey => $contentItem)
                                    <div class="subsection">
                                        <h3 class="subsection-title">{{ $contentItem['title'] }}</h3>
                                        <p class="subsection-description">{{ $contentItem['description'] }}</p>
                                        
                                        @if(isset($contentItem['steps']))
                                            <div class="steps-container">
                                                <h4>Langkah-langkah:</h4>
                                                <ol class="steps-list">
                                                    @foreach($contentItem['steps'] as $step)
                                                        <li>{{ $step }}</li>
                                                    @endforeach
                                                </ol>
                                            </div>
                                        @endif
                                    </div>
                                @endforeach
                            @elseif($sectionKey === 'catatan_tambahan')
                                @foreach($section['content'] as $contentKey => $contentItem)
                                    <div class="subsection">
                                        <h3 class="subsection-title">{{ $contentItem['title'] }}</h3>
                                        <p class="subsection-description">{{ $contentItem['description'] }}</p>
                                    </div>
                                @endforeach
                            @endif

                            @if(isset($section['kesimpulan']))
                                <div class="section-conclusion">
                                    <div class="conclusion-header">
                                        <i class="fas fa-lightbulb"></i>
                                        <h4>Kesimpulan</h4>
                                    </div>
                                    <p class="conclusion-text">{{ $section['kesimpulan'] }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Sidebar -->
                <div class="col-lg-3">
                    <div class="sidebar">
                        <!-- Table of Contents -->
                        <div class="toc-card">
                            <h3 class="toc-title">
                                <i class="fas fa-list"></i>
                                Daftar Isi
                            </h3>
                            <ul class="toc-list">
                                @foreach($sipwData['sections'] as $sectionKey => $section)
                                <li>
                                    <a href="#{{ $sectionKey }}" class="toc-link" onclick="scrollToSection('{{ $sectionKey }}')">
                                        <i class="{{ $section['icon'] }}"></i>
                                        {{ $section['title'] }}
                                    </a>
                                </li>
                                @endforeach
                            </ul>
                        </div>

                        <!-- Quick Actions -->
                        <div class="quick-actions-card">
                            <h3 class="card-title">
                                <i class="fas fa-bolt"></i>
                                Aksi Cepat
                            </h3>
                            <div class="action-buttons">
                                <a href="{{ $sipwData['pdf_link'] }}" class="action-btn" target="_blank">
                                    <i class="fas fa-download"></i>
                                    Download PDF
                                </a>
                                <button class="action-btn" onclick="printPage()">
                                    <i class="fas fa-print"></i>
                                    Cetak Halaman
                                </button>
                                <button class="action-btn" onclick="shareContent()">
                                    <i class="fas fa-share"></i>
                                    Bagikan
                                </button>
                            </div>
                        </div>

                        <!-- Related Materials -->
                        <div class="related-materials-card">
                            <h3 class="card-title">
                                <i class="fas fa-book"></i>
                                Materi Terkait
                            </h3>
                            <div class="related-list">
                                <a href="{{ route('organisasi-pengolahan') }}" class="related-item">
                                    <i class="fas fa-users"></i>
                                    <span>Organisasi Pengolahan</span>
                                </a>
                                <a href="{{ route('materi-mekanisme-pen') }}" class="related-item">
                                    <i class="fas fa-cogs"></i>
                                    <span>Mekanisme Pengolahan</span>
                                </a>
                                <a href="{{ route('materials') }}" class="related-item">
                                    <i class="fas fa-arrow-left"></i>
                                    <span>Kembali ke Materi</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 4rem 0;
        margin-bottom: 0;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .page-subtitle {
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
        opacity: 0.9;
    }

    .page-description {
        font-size: 1rem;
        opacity: 0.8;
        line-height: 1.6;
    }

    .material-info {
        background: rgba(255, 255, 255, 0.1);
        padding: 1.5rem;
        border-radius: 12px;
        backdrop-filter: blur(10px);
    }

    .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        font-size: 0.95rem;
    }

    .info-item i {
        margin-right: 0.5rem;
        width: 20px;
    }

    .breadcrumb-section {
        background: #f8f9fa;
        padding: 1rem 0;
        border-bottom: 1px solid #e9ecef;
    }

    .content-sections {
        padding: 3rem 0;
    }

    .content-section {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
    }

    .section-header {
        border-bottom: 2px solid #667eea;
        padding-bottom: 1rem;
        margin-bottom: 2rem;
    }

    .section-title {
        color: #2d3748;
        font-size: 1.75rem;
        font-weight: 600;
        margin: 0;
    }

    .section-title i {
        color: #667eea;
        margin-right: 0.75rem;
    }

    .subsection {
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #667eea;
    }

    .subsection-title {
        color: #2d3748;
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .subsection-description {
        color: #4a5568;
        line-height: 1.6;
        margin-bottom: 1rem;
    }

    .content-list, .steps-list, .features-list, .roles-list, .process-list {
        margin: 1rem 0;
        padding-left: 1.5rem;
    }

    .content-list li, .features-list li, .roles-list li, .process-list li {
        margin-bottom: 0.5rem;
        color: #4a5568;
        line-height: 1.6;
    }

    .steps-list {
        counter-reset: step-counter;
    }

    .steps-list li {
        counter-increment: step-counter;
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: white;
        border-radius: 6px;
        border-left: 3px solid #667eea;
        position: relative;
    }

    .steps-list li::before {
        content: counter(step-counter);
        position: absolute;
        left: -15px;
        top: 50%;
        transform: translateY(-50%);
        background: #667eea;
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .section-conclusion {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 8px;
        margin-top: 2rem;
    }

    .conclusion-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .conclusion-header i {
        margin-right: 0.5rem;
        font-size: 1.2rem;
    }

    .conclusion-header h4 {
        margin: 0;
        font-weight: 600;
    }

    .conclusion-text {
        line-height: 1.6;
        margin: 0;
        opacity: 0.95;
    }

    .sidebar {
        position: sticky;
        top: 2rem;
    }

    .toc-card, .quick-actions-card, .related-materials-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
    }

    .toc-title, .card-title {
        color: #2d3748;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    .toc-title i, .card-title i {
        margin-right: 0.5rem;
        color: #667eea;
    }

    .toc-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .toc-link {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        color: #4a5568;
        text-decoration: none;
        border-radius: 6px;
        transition: all 0.2s;
        margin-bottom: 0.25rem;
    }

    .toc-link:hover {
        background: #667eea;
        color: white;
        transform: translateX(5px);
    }

    .toc-link i {
        margin-right: 0.75rem;
        width: 16px;
    }

    .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .action-btn {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        color: #4a5568;
        text-decoration: none;
        transition: all 0.2s;
        cursor: pointer;
        font-size: 0.9rem;
    }

    .action-btn:hover {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }

    .action-btn i {
        margin-right: 0.5rem;
    }

    .related-list {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .related-item {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        color: #4a5568;
        text-decoration: none;
        border-radius: 6px;
        transition: all 0.2s;
        border: 1px solid #e9ecef;
    }

    .related-item:hover {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }

    .related-item i {
        margin-right: 0.75rem;
        width: 16px;
    }

    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }
        
        .content-section {
            padding: 1.5rem;
        }
        
        .sidebar {
            position: static;
            margin-top: 2rem;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    function scrollToSection(sectionId) {
        const element = document.getElementById(sectionId);
        if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }

    function printPage() {
        window.print();
    }

    function shareContent() {
        if (navigator.share) {
            navigator.share({
                title: '{{ $sipwData["title"] }}',
                text: '{{ $sipwData["description"] }}',
                url: window.location.href
            });
        } else {
            // Fallback: copy URL to clipboard
            navigator.clipboard.writeText(window.location.href).then(() => {
                alert('Link telah disalin ke clipboard!');
            });
        }
    }

    // Highlight active section in TOC
    window.addEventListener('scroll', function() {
        const sections = document.querySelectorAll('.content-section');
        const tocLinks = document.querySelectorAll('.toc-link');
        
        let current = '';
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if (pageYOffset >= sectionTop - 200) {
                current = section.getAttribute('id');
            }
        });
        
        tocLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === '#' + current) {
                link.classList.add('active');
            }
        });
    });
</script>
@endpush