@extends('layouts.app')

@section('title', '<PERSON><PERSON> - <PERSON><PERSON><PERSON>golahan Wilkerstat SE2026')

@section('head')
<link rel="stylesheet" href="{{ asset('css/training-modern.css') }}">
@endsection

@section('content')
<div class="container-fluid">
    <!-- Modern Page Header -->
    <div class="modern-page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="modern-page-title">Materi Pelatihan Pengolahan Wilkerstat SE2026</h1>
                    <p class="modern-page-subtitle">Hub Pembelajaran Terpadu</p>
                    <p class="modern-page-description">Akses semua materi pelatihan, modul pembelajaran, dan panduan lengkap untuk pengolahan data Wilkerstat SE2026 dalam satu tempat yang terorganisir dan mudah digunakan.</p>
                </div>
                <div class="col-lg-4">
                    <div class="modern-material-info mt-4">
                        <div class="modern-info-badges">
                            <div class="modern-info-badge">
                                <i class="fas fa-book-open"></i>
                                <span>5 Modul</span>
                            </div>
                            <div class="modern-info-badge">
                                <i class="fas fa-file-pdf"></i>
                                <span>{{ $availableFilesCount }} PDF</span>
                            </div>
                        </div>
                        <a href="{{ $googleDriveLink }}" class="modern-download-btn" target="_blank">
                            <i class="fab fa-google-drive"></i>
                            Google Drive Lengkap
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Breadcrumb -->
    <div class="modern-breadcrumb-section">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="modern-breadcrumb">
                    <li class="modern-breadcrumb-item">
                        <a href="{{ route('home') }}" class="modern-breadcrumb-link">Beranda</a>
                    </li>
                    <li class="modern-breadcrumb-item">
                        <span class="modern-breadcrumb-current">Materi Pelatihan</span>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Modern Content Layout -->
    <div class="modern-content-layout">
        <div class="container">
            <!-- Training Modules Section -->
            <div class="modern-section mb-8">
                <div class="modern-section-header text-center mb-6">
                    <h2 class="modern-section-title">
                        <i class="fas fa-graduation-cap"></i>
                        Modul Pelatihan
                    </h2>
                    <p class="modern-section-subtitle">
                        Pilih modul pelatihan sesuai dengan kebutuhan pembelajaran Anda
                    </p>
                </div>
                
                <div class="row g-4">
                    <!-- Pendahuluan Module -->
                    <div class="col-lg-6 col-xl-4">
                        <div class="modern-training-card">
                            <div class="modern-card-header">
                                <div class="modern-card-icon pendahuluan">
                                    <i class="fas fa-play-circle"></i>
                                </div>
                                <div class="modern-card-badge">
                                    <span>Modul 1</span>
                                </div>
                            </div>
                            <div class="modern-card-content">
                                <h3 class="modern-card-title">Pendahuluan</h3>
                                <p class="modern-card-description">
                                    Latar belakang, maksud dan tujuan, landasan hukum, serta instrumen dan perangkat SE2026.
                                </p>
                                <div class="modern-card-meta">
                                    <div class="modern-meta-item">
                                        <i class="fas fa-clock"></i>
                                        <span>45 menit</span>
                                    </div>
                                    <div class="modern-meta-item">
                                        <i class="fas fa-signal"></i>
                                        <span>Pemula</span>
                                    </div>
                                </div>
                            </div>
                            <div class="modern-card-footer">
                                <a href="{{ route('pendahuluan') }}" class="modern-btn modern-btn-primary">
                                    <i class="fas fa-book-open"></i>
                                    Pelajari Modul
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Organisasi Pengolahan Module -->
                    <div class="col-lg-6 col-xl-4">
                        <div class="modern-training-card">
                            <div class="modern-card-header">
                                <div class="modern-card-icon organisasi">
                                    <i class="fas fa-sitemap"></i>
                                </div>
                                <div class="modern-card-badge">
                                    <span>Modul 2</span>
                                </div>
                            </div>
                            <div class="modern-card-content">
                                <h3 class="modern-card-title">Organisasi Pengolahan</h3>
                                <p class="modern-card-description">
                                    Struktur organisasi dan pembagian tugas dalam pengolahan data Wilkerstat.
                                </p>
                                <div class="modern-card-meta">
                                    <div class="modern-meta-item">
                                        <i class="fas fa-clock"></i>
                                        <span>45 menit</span>
                                    </div>
                                    <div class="modern-meta-item">
                                        <i class="fas fa-signal"></i>
                                        <span>Menengah</span>
                                    </div>
                                </div>
                            </div>
                            <div class="modern-card-footer">
                                <a href="{{ route('organisasi-pengolahan') }}" class="modern-btn modern-btn-primary">
                                    <i class="fas fa-book-open"></i>
                                    Pelajari Modul
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Mekanisme Pengolahan Module -->
                    <div class="col-lg-6 col-xl-4">
                        <div class="modern-training-card">
                            <div class="modern-card-header">
                                <div class="modern-card-icon mekanisme">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <div class="modern-card-badge">
                                    <span>Modul 3</span>
                                </div>
                            </div>
                            <div class="modern-card-content">
                                <h3 class="modern-card-title">Mekanisme Pengolahan</h3>
                                <p class="modern-card-description">
                                    Prosedur dan mekanisme pengolahan data secara sistematis dan terstruktur.
                                </p>
                                <div class="modern-card-meta">
                                    <div class="modern-meta-item">
                                        <i class="fas fa-clock"></i>
                                        <span>60 menit</span>
                                    </div>
                                    <div class="modern-meta-item">
                                        <i class="fas fa-signal"></i>
                                        <span>Menengah</span>
                                    </div>
                                </div>
                            </div>
                            <div class="modern-card-footer">
                                <a href="{{ route('materi-mekanisme-pen') }}" class="modern-btn modern-btn-primary">
                                    <i class="fas fa-book-open"></i>
                                    Pelajari Modul
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- SIPW SE2026 Module -->
                    <div class="col-lg-6 col-xl-4">
                        <div class="modern-training-card">
                            <div class="modern-card-header">
                                <div class="modern-card-icon sipw">
                                    <i class="fas fa-desktop"></i>
                                </div>
                                <div class="modern-card-badge">
                                    <span>Modul 4</span>
                                </div>
                            </div>
                            <div class="modern-card-content">
                                <h3 class="modern-card-title">SIPW SE2026</h3>
                                <p class="modern-card-description">
                                    Panduan lengkap penggunaan aplikasi SiPW untuk pemutakhiran data Wilkerstat.
                                </p>
                                <div class="modern-card-meta">
                                    <div class="modern-meta-item">
                                        <i class="fas fa-clock"></i>
                                        <span>90 menit</span>
                                    </div>
                                    <div class="modern-meta-item">
                                        <i class="fas fa-signal"></i>
                                        <span>Lanjutan</span>
                                    </div>
                                </div>
                            </div>
                            <div class="modern-card-footer">
                                <a href="{{ route('sipw-se2026') }}" class="modern-btn modern-btn-primary">
                                    <i class="fas fa-book-open"></i>
                                    Pelajari Modul
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Pengolahan Peta Module -->
                    <div class="col-lg-6 col-xl-4">
                        <div class="modern-training-card">
                            <div class="modern-card-header">
                                <div class="modern-card-icon peta">
                                    <i class="fas fa-map"></i>
                                </div>
                                <div class="modern-card-badge">
                                    <span>Modul 5</span>
                                </div>
                            </div>
                            <div class="modern-card-content">
                                <h3 class="modern-card-title">Pengolahan Peta</h3>
                                <p class="modern-card-description">
                                    Teknik dan metode pengolahan peta digital untuk keperluan Wilkerstat.
                                </p>
                                <div class="modern-card-meta">
                                    <div class="modern-meta-item">
                                        <i class="fas fa-clock"></i>
                                        <span>75 menit</span>
                                    </div>
                                    <div class="modern-meta-item">
                                        <i class="fas fa-signal"></i>
                                        <span>Lanjutan</span>
                                    </div>
                                </div>
                            </div>
                            <div class="modern-card-footer">
                                <a href="{{ route('pengolahan-peta') }}" class="modern-btn modern-btn-primary">
                                    <i class="fas fa-book-open"></i>
                                    Pelajari Modul
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Buku Pedoman Section -->
                    <div class="col-lg-6 col-xl-4">
                        <div class="modern-training-card modern-manual-card">
                            <div class="modern-card-header">
                                <div class="modern-card-icon manual">
                                    <i class="fas fa-book"></i>
                                </div>
                                <div class="modern-card-badge manual">
                                    <span>Manual</span>
                                </div>
                            </div>
                            <div class="modern-card-content">
                                <h3 class="modern-card-title">Buku Pedoman Lengkap</h3>
                                <p class="modern-card-description">
                                    Panduan komprehensif berisi semua materi pelatihan dalam satu dokumen lengkap.
                                </p>
                                <div class="modern-card-meta">
                                    <div class="modern-meta-item">
                                        <i class="fas fa-file-pdf"></i>
                                        <span>PDF Lengkap</span>
                                    </div>
                                    <div class="modern-meta-item">
                                        <i class="fas fa-download"></i>
                                        <span>Siap Unduh</span>
                                    </div>
                                </div>
                            </div>
                            <div class="modern-card-footer">
                                <a href="{{ asset('bahanajar/[FINAL] Buku 3 Pedoman Pengolahan Pemutakhiran Wilkerstat SE2026.pdf') }}" class="modern-btn modern-btn-secondary" target="_blank">
                                    <i class="fas fa-download"></i>
                                    Download PDF
                                </a>
                                <a href="{{ $googleDriveLink }}" class="modern-btn modern-btn-outline" target="_blank">
                                    <i class="fab fa-google-drive"></i>
                                    Google Drive
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

@endsection

@section('scripts')
<script src="{{ asset('js/training-modern.js') }}"></script>
<script>
// Modern Materials Hub JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize modern animations
    initializeModernAnimations();
    
    // Add hover effects to training cards
    const trainingCards = document.querySelectorAll('.modern-training-card');
    trainingCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
            this.style.boxShadow = '0 20px 40px rgba(0,0,0,0.1)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 10px 30px rgba(0,0,0,0.08)';
        });
    });
    
    // Add special styling for manual card
    const manualCard = document.querySelector('.modern-manual-card');
    if (manualCard) {
        manualCard.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
            this.style.boxShadow = '0 25px 50px rgba(52, 152, 219, 0.15)';
        });
        
        manualCard.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 10px 30px rgba(0,0,0,0.08)';
        });
    }
    
    // Track module access
    const moduleLinks = document.querySelectorAll('.modern-training-card .modern-btn-primary');
    moduleLinks.forEach(link => {
        link.addEventListener('click', function() {
            const cardTitle = this.closest('.modern-training-card').querySelector('.modern-card-title').textContent;
            console.log('Accessing module:', cardTitle);
            // Add analytics tracking here if needed
        });
    });
    
    // Track manual downloads
    const downloadLinks = document.querySelectorAll('.modern-btn-secondary, .modern-btn-outline');
    downloadLinks.forEach(link => {
        link.addEventListener('click', function() {
            const action = this.textContent.includes('Download') ? 'download' : 'drive_access';
            console.log('Manual action:', action);
            // Add analytics tracking here if needed
        });
    });
});

function initializeModernAnimations() {
    // Staggered animation for cards
    const cards = document.querySelectorAll('.modern-training-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // Animate section header
    const sectionHeader = document.querySelector('.modern-section-header');
    if (sectionHeader) {
        sectionHeader.style.opacity = '0';
        sectionHeader.style.transform = 'translateY(-20px)';
        
        setTimeout(() => {
            sectionHeader.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
            sectionHeader.style.opacity = '1';
            sectionHeader.style.transform = 'translateY(0)';
        }, 200);
    }
}
</script>
@endsection
@endsection

@push('styles')
<style>
    /* Page Header */
    .page-header {
        background: linear-gradient(135deg, var(--bg-white) 0%, #f8fafc 100%);
        border-radius: 0.75rem;
        padding: 2rem;
        border: 1px solid var(--border-color);
    }

    .page-header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 2rem;
    }

    .page-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .page-title i {
        color: var(--primary-color);
    }

    .page-subtitle {
        color: var(--text-secondary);
        font-size: 1.125rem;
        line-height: 1.6;
    }

    /* Overview Cards */
    .overview-card {
        background: var(--bg-white);
        border-radius: 0.75rem;
        padding: 1.5rem;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
        display: flex;
        align-items: center;
        gap: 1rem;
        transition: all 0.3s ease;
    }

    .overview-card:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
    }

    .overview-icon {
        width: 50px;
        height: 50px;
        border-radius: 0.5rem;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
    }

    .overview-number {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--text-primary);
        line-height: 1;
    }

    .overview-label {
        color: var(--text-secondary);
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Google Drive Access Section */
    .drive-access-card {
        background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
        border-radius: 0.75rem;
        padding: 2rem;
        color: white;
        box-shadow: var(--shadow-md);
        transition: all 0.3s ease;
    }

    .drive-access-card:hover {
        box-shadow: var(--shadow-lg);
        transform: translateY(-2px);
    }

    .drive-access-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 2rem;
    }

    .drive-access-info {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        flex: 1;
    }

    .drive-icon {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 0.75rem;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
    }

    .drive-text h3 {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: white;
    }

    .drive-text p {
        color: rgba(255, 255, 255, 0.9);
        font-size: 0.875rem;
        line-height: 1.5;
        margin: 0;
    }

    .drive-actions .btn {
        background: white;
        color: #4285f4;
        border: none;
        font-weight: 600;
    }

    .drive-actions .btn:hover {
        background: rgba(255, 255, 255, 0.9);
        transform: translateY(-1px);
    }

    /* Search Section */
    .search-card {
        background: var(--bg-white);
        border-radius: 0.75rem;
        padding: 1.5rem;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
    }

    .search-controls {
        display: grid;
        grid-template-columns: 1fr auto auto;
        gap: 1rem;
        align-items: center;
    }

    .search-input-group {
        position: relative;
    }

    .search-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-secondary);
    }

    .search-input {
        width: 100%;
        padding: 0.75rem 1rem 0.75rem 2.5rem;
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        font-size: 0.875rem;
        transition: all 0.3s ease;
    }

    .search-input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    /* Module Sections */
    .module-section {
        background: var(--bg-white);
        border-radius: 0.75rem;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
        overflow: hidden;
    }

    .module-header {
        padding: 1.5rem;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .module-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .module-title i {
        color: var(--primary-color);
    }

    .module-stats {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .module-count {
        font-size: 0.875rem;
        color: var(--text-secondary);
        background: var(--bg-white);
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
    }

    .module-types {
        display: flex;
        gap: 0.5rem;
    }

    .type-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-weight: 500;
        color: white;
    }

    .type-pdf { background: #ef4444; }
    .type-video { background: #8b5cf6; }

    .module-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
    }

    .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }

    .btn-outline {
        background: transparent;
        border: 1px solid var(--border-color);
        color: var(--text-primary);
    }

    .btn-outline:hover {
        background: var(--bg-light);
        border-color: var(--primary-color);
    }

    .toggle-icon {
        transition: transform 0.3s ease;
    }

    .toggle-icon.rotated {
        transform: rotate(180deg);
    }

    /* Module Content */
    .module-content {
        padding: 1.5rem;
        transition: all 0.3s ease;
    }

    .module-content.collapsed {
        display: none;
    }

    .materials-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 1.5rem;
    }

    /* Material Cards */
    .material-card {
        background: var(--bg-white);
        border: 1px solid var(--border-color);
        border-radius: 0.75rem;
        overflow: hidden;
        transition: all 0.3s ease;
        position: relative;
    }

    .material-card:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
        border-color: var(--primary-color);
    }

    .material-header {
        padding: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    }

    .material-type-badge {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem 0.75rem;
        border-radius: 0.5rem;
        font-size: 0.75rem;
        font-weight: 600;
        color: white;
    }

    .material-actions {
        display: flex;
        gap: 0.5rem;
    }

    .btn-action {
        width: 32px;
        height: 32px;
        border-radius: 0.375rem;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: var(--bg-white);
        color: var(--text-secondary);
    }

    .btn-favorite:hover {
        color: #ef4444;
        background: #fef2f2;
    }

    .btn-favorite.active {
        color: #ef4444;
        background: #fef2f2;
    }

    .btn-favorite.active i {
        font-weight: 900;
    }

    .material-content {
        padding: 1rem;
    }

    .material-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        line-height: 1.4;
    }

    .material-description {
        color: var(--text-secondary);
        font-size: 0.875rem;
        line-height: 1.5;
        margin-bottom: 1rem;
    }

    .material-meta {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.75rem;
        color: var(--text-secondary);
    }

    /* Difficulty Levels */
    .difficulty-pemula {
        color: #10b981 !important;
        background: #ecfdf5;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-weight: 500;
    }

    .difficulty-menengah {
        color: #f59e0b !important;
        background: #fffbeb;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-weight: 500;
    }

    .difficulty-lanjutan {
        color: #ef4444 !important;
        background: #fef2f2;
        padding: 0.25rem 0.5rem;
        border-radius: 0.375rem;
        font-weight: 500;
    }

    .material-footer {
        padding: 1rem;
        border-top: 1px solid var(--border-color);
        display: flex;
        gap: 0.5rem;
    }

    .btn-block {
        flex: 1;
    }

    /* Quick Access Sidebar */
    .quick-access {
        position: fixed;
        top: 50%;
        right: -300px;
        transform: translateY(-50%);
        width: 300px;
        background: var(--bg-white);
        border: 1px solid var(--border-color);
        border-radius: 0.75rem 0 0 0.75rem;
        box-shadow: var(--shadow-lg);
        transition: right 0.3s ease;
        z-index: 100;
    }

    .quick-access.open {
        right: 0;
    }

    .quick-access-header {
        padding: 1rem;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        border-radius: 0.75rem 0 0 0;
    }

    .quick-access-toggle {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        width: 32px;
        height: 32px;
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .quick-access-toggle:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    .quick-access-content {
        padding: 1rem;
        max-height: 400px;
        overflow-y: auto;
    }

    .quick-section {
        margin-bottom: 1.5rem;
    }

    .quick-section h5 {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
    }

    .empty-state {
        font-size: 0.75rem;
        color: var(--text-secondary);
        font-style: italic;
        text-align: center;
        padding: 1rem 0;
    }

    /* Modal */
    .modal-large .modal-content {
        max-width: 800px;
    }

    /* No Results */
    .no-results {
        padding: 3rem;
        text-align: center;
        background: var(--bg-white);
        border-radius: 0.75rem;
        border: 1px solid var(--border-color);
    }

    .no-results-content i {
        font-size: 3rem;
        color: var(--text-secondary);
        margin-bottom: 1rem;
    }

    .no-results-content h3 {
        color: var(--text-primary);
        margin-bottom: 0.5rem;
    }

    .no-results-content p {
        color: var(--text-secondary);
        margin-bottom: 1.5rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .page-header-content {
            flex-direction: column;
            align-items: stretch;
        }

        .search-controls {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .module-header {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        .module-actions {
            justify-content: flex-end;
        }

        .materials-grid {
            grid-template-columns: 1fr;
        }

        .grid-cols-md-4 {
            grid-template-columns: repeat(2, 1fr);
        }

        .quick-access {
            display: none;
        }
    }

    @media (max-width: 480px) {
        .grid-cols-md-4 {
            grid-template-columns: 1fr;
        }

        .material-footer {
            flex-direction: column;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    let favorites = JSON.parse(localStorage.getItem('materialFavorites') || '[]');
    let recentAccess = JSON.parse(localStorage.getItem('materialRecent') || '[]');

    // Search and filter functionality
    function filterMaterials() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const typeFilter = document.getElementById('typeFilter').value;
        
        const materialCards = document.querySelectorAll('.material-card');
        const moduleSection = document.querySelectorAll('.module-section');
        
        let hasVisibleResults = false;
        
        moduleSection.forEach(module => {
            let hasVisibleCards = false;
            const cards = module.querySelectorAll('.material-card');
            
            cards.forEach(card => {
                const title = card.dataset.title;
                const description = card.dataset.description;
                const type = card.dataset.type;
                
                const matchesSearch = !searchTerm || title.includes(searchTerm) || description.includes(searchTerm);
                const matchesType = !typeFilter || type === typeFilter;
                
                if (matchesSearch && matchesType) {
                    card.style.display = 'block';
                    hasVisibleCards = true;
                    hasVisibleResults = true;
                } else {
                    card.style.display = 'none';
                }
            });
            
            module.style.display = hasVisibleCards ? 'block' : 'none';
        });
        
        document.getElementById('noResults').style.display = hasVisibleResults ? 'none' : 'block';
        document.querySelector('.materials-section').style.display = hasVisibleResults ? 'block' : 'none';
    }

    // Reset search
    function resetSearch() {
        document.getElementById('searchInput').value = '';
        document.getElementById('typeFilter').value = '';
        filterMaterials();
    }

    // Toggle module visibility
    function toggleModule(index) {
        const content = document.getElementById(`module-${index}`);
        const icon = document.getElementById(`toggle-${index}`);
        
        content.classList.toggle('collapsed');
        icon.classList.toggle('rotated');
    }

    // Toggle favorite
    function toggleFavorite(button) {
        const card = button.closest('.material-card');
        const title = card.querySelector('.material-title').textContent;
        const icon = button.querySelector('i');
        
        if (favorites.includes(title)) {
            favorites = favorites.filter(fav => fav !== title);
            icon.className = 'far fa-heart';
            button.classList.remove('active');
        } else {
            favorites.push(title);
            icon.className = 'fas fa-heart';
            button.classList.add('active');
        }
        
        localStorage.setItem('materialFavorites', JSON.stringify(favorites));
        updateQuickAccess();
    }

    // Track download
    function trackDownload(title) {
        // Add to recent access
        recentAccess = recentAccess.filter(item => item !== title);
        recentAccess.unshift(title);
        recentAccess = recentAccess.slice(0, 5); // Keep only last 5
        
        localStorage.setItem('materialRecent', JSON.stringify(recentAccess));
        updateQuickAccess();
    }

    // Preview material
    function previewMaterial(title, description, type) {
        document.getElementById('previewTitle').textContent = `Preview: ${title}`;
        
        const previewBody = document.getElementById('previewBody');
        previewBody.innerHTML = `
            <div class="preview-content">
                <div class="preview-header">
                    <div class="preview-type type-${type.toLowerCase()}">
                        ${type === 'PDF' ? '<i class="fas fa-file-pdf"></i>' : '<i class="fas fa-video"></i>'}
                        ${type}
                    </div>
                </div>
                <h3 class="preview-title">${title}</h3>
                <p class="preview-description">${description}</p>
                <div class="preview-info">
                    <div class="info-item">
                        <i class="fas fa-info-circle"></i>
                        <span>Materi ini disediakan melalui tautan eksternal</span>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>Pastikan koneksi internet stabil untuk akses optimal</span>
                    </div>
                    ${type === 'Video' ? '<div class="info-item"><i class="fas fa-clock"></i><span>Durasi estimasi: 30-45 menit</span></div>' : ''}
                </div>
            </div>
            <style>
                .preview-content {
                    padding: 1rem 0;
                }
                .preview-header {
                    margin-bottom: 1rem;
                }
                .preview-type {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.5rem;
                    padding: 0.5rem 1rem;
                    border-radius: 0.5rem;
                    font-weight: 600;
                    color: white;
                }
                .preview-title {
                    font-size: 1.25rem;
                    font-weight: 600;
                    margin-bottom: 1rem;
                    color: var(--text-primary);
                }
                .preview-description {
                    color: var(--text-secondary);
                    line-height: 1.6;
                    margin-bottom: 1.5rem;
                }
                .preview-info {
                    background: var(--bg-light);
                    padding: 1rem;
                    border-radius: 0.5rem;
                }
                .info-item {
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    margin-bottom: 0.5rem;
                    font-size: 0.875rem;
                    color: var(--text-secondary);
                }
                .info-item:last-child {
                    margin-bottom: 0;
                }
                .info-item i {
                    color: var(--primary-color);
                }
            </style>
        `;
        
        document.getElementById('previewModal').style.display = 'block';
    }

    // Close preview
    function closePreview() {
        document.getElementById('previewModal').style.display = 'none';
    }

    // Download module
    function downloadModule(moduleName) {
        alert(`Mengunduh semua materi dari ${moduleName}...\nFitur ini akan mengarahkan ke halaman unduhan batch.`);
    }

    // Download all materials
    function downloadAll() {
        alert('Mengunduh semua materi pelatihan...\nFitur ini akan mengarahkan ke halaman unduhan lengkap.');
    }

    // Toggle quick access
    function toggleQuickAccess() {
        const quickAccess = document.getElementById('quickAccess');
        quickAccess.classList.toggle('open');
        
        const icon = document.querySelector('.quick-access-toggle i');
        if (quickAccess.classList.contains('open')) {
            icon.className = 'fas fa-chevron-left';
        } else {
            icon.className = 'fas fa-chevron-right';
        }
    }

    // Update quick access content
    function updateQuickAccess() {
        // Update favorites
        const favoritesList = document.getElementById('favoritesList');
        if (favorites.length === 0) {
            favoritesList.innerHTML = '<p class="empty-state">Belum ada materi favorit</p>';
        } else {
            favoritesList.innerHTML = favorites.map(fav => 
                `<div class="quick-item">${fav}</div>`
            ).join('');
        }
        
        // Update recent access
        const recentList = document.getElementById('recentList');
        if (recentAccess.length === 0) {
            recentList.innerHTML = '<p class="empty-state">Belum ada aktivitas</p>';
        } else {
            recentList.innerHTML = recentAccess.map(item => 
                `<div class="quick-item">${item}</div>`
            ).join('');
        }
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        // Search event listeners
        document.getElementById('searchInput').addEventListener('input', filterMaterials);
        document.getElementById('typeFilter').addEventListener('change', filterMaterials);
        
        // Initialize favorites
        favorites.forEach(title => {
            const cards = document.querySelectorAll('.material-card');
            cards.forEach(card => {
                const cardTitle = card.querySelector('.material-title').textContent;
                if (cardTitle === title) {
                    const button = card.querySelector('.btn-favorite');
                    const icon = button.querySelector('i');
                    icon.className = 'fas fa-heart';
                    button.classList.add('active');
                }
            });
        });
        
        // Update quick access
        updateQuickAccess();
        
        // Modal close on outside click
        document.getElementById('previewModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePreview();
            }
        });
    });
</script>
@endpush