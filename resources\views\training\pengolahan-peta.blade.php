@extends('layouts.app')

@section('title', $pengolahanPetaData['title'])

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">{{ $pengolahanPetaData['title'] }}</h1>
                    <p class="page-subtitle">{{ $pengolahanPetaData['subtitle'] }}</p>
                    <p class="page-description">{{ $pengolahanPetaData['description'] }}</p>
                </div>
                <div class="col-lg-4 text-end">
                    <div class="material-info">
                        <div class="info-item">
                            <i class="fas fa-clock"></i>
                            <span>{{ $pengolahanPetaData['duration'] }}</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-signal"></i>
                            <span>{{ $pengolahanPetaData['difficulty'] }}</span>
                        </div>
                        <a href="{{ $pengolahanPetaData['pdf_link'] }}" class="btn btn-primary" target="_blank">
                            <i class="fas fa-download"></i> Download PDF
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Breadcrumb -->
    <div class="breadcrumb-section">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('home') }}">Beranda</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('materials') }}">Materi</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ $pengolahanPetaData['title'] }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Content Sections -->
    <div class="content-sections">
        <div class="container">
            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-9">
                    @foreach($pengolahanPetaData['sections'] as $sectionKey => $section)
                    <div class="content-section" id="{{ $sectionKey }}">
                        <div class="section-header">
                            <h2 class="section-title">
                                <i class="{{ $section['icon'] }}"></i>
                                {{ $section['title'] }}
                            </h2>
                        </div>
                        
                        <div class="section-content">
                            @if($sectionKey === 'alat_bahan')
                                <!-- Alat dan Bahan Content -->
                                <div class="tools-materials-section">
                                    @if(isset($section['perangkat_keras']))
                                    <div class="tool-category">
                                        <h4>
                                            <i class="{{ $section['perangkat_keras']['icon'] }}"></i>
                                            {{ $section['perangkat_keras']['title'] }}
                                        </h4>
                                        <ul class="tool-list">
                                            @foreach($section['perangkat_keras']['items'] as $item)
                                            <li>{{ $item }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                    @endif
                                    
                                    @if(isset($section['perangkat_lunak']))
                                    <div class="tool-category">
                                        <h4>
                                            <i class="{{ $section['perangkat_lunak']['icon'] }}"></i>
                                            {{ $section['perangkat_lunak']['title'] }}
                                        </h4>
                                        <ul class="tool-list">
                                            @foreach($section['perangkat_lunak']['items'] as $item)
                                            <li>{{ $item }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                    @endif
                                    
                                    @if(isset($section['data_peta']))
                                    <div class="tool-category">
                                        <h4>
                                            <i class="{{ $section['data_peta']['icon'] }}"></i>
                                            {{ $section['data_peta']['title'] }}
                                        </h4>
                                        <ul class="tool-list">
                                            @foreach($section['data_peta']['items'] as $item)
                                            <li>{{ $item }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                    @endif
                                </div>
                                
                            @elseif($sectionKey === 'persiapan')
                                <!-- Persiapan Content -->
                                <div class="preparation-steps">
                                    @foreach($section['content'] as $step)
                                    <div class="step-item">
                                        <i class="fas fa-check-circle"></i>
                                        <span>{{ $step }}</span>
                                    </div>
                                    @endforeach
                                </div>
                                
                            @elseif($sectionKey === 'georeferencing')
                                <!-- Georeferencing Content -->
                                <div class="content-text">
                                    @foreach($section['content'] as $content)
                                        <p>{{ $content }}</p>
                                    @endforeach
                                </div>
                                
                                @if(isset($section['langkah']))
                                <div class="steps-section">
                                    <h4>Langkah-langkah Georeferencing:</h4>
                                    <div class="row">
                                        @foreach($section['langkah'] as $stepKey => $step)
                                        <div class="col-md-6">
                                            <div class="step-card">
                                                <div class="card-header">
                                                    <i class="{{ $step['icon'] }}"></i>
                                                    <h5>{{ $step['title'] }}</h5>
                                                </div>
                                                <div class="card-body">
                                                    <ul>
                                                        @foreach($step['items'] as $item)
                                                        <li>{{ $item }}</li>
                                                        @endforeach
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                                @endif
                                
                            @elseif($sectionKey === 'editing')
                                <!-- Editing Content -->
                                <div class="content-text">
                                    @foreach($section['content'] as $content)
                                        <p>{{ $content }}</p>
                                    @endforeach
                                </div>
                                
                                @if(isset($section['teknik']))
                                <div class="techniques-section">
                                    <h4>Teknik Editing:</h4>
                                    <div class="techniques-grid">
                                        @foreach($section['teknik'] as $technique)
                                        <div class="technique-item">
                                            <i class="{{ $technique['icon'] }}"></i>
                                            <h5>{{ $technique['title'] }}</h5>
                                            <p>{{ $technique['description'] }}</p>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                                @endif
                                
                            @elseif($sectionKey === 'cleaning')
                                <!-- Cleaning Content -->
                                <div class="content-text">
                                    @foreach($section['content'] as $content)
                                        <p>{{ $content }}</p>
                                    @endforeach
                                </div>
                                
                                @if(isset($section['proses']))
                                <div class="cleaning-processes">
                                    @foreach($section['proses'] as $process)
                                    <div class="process-item">
                                        <div class="process-header">
                                            <i class="{{ $process['icon'] }}"></i>
                                            <h5>{{ $process['title'] }}</h5>
                                        </div>
                                        <div class="process-content">
                                            <p>{{ $process['description'] }}</p>
                                            @if(isset($process['steps']))
                                            <ul>
                                                @foreach($process['steps'] as $step)
                                                <li>{{ $step }}</li>
                                                @endforeach
                                            </ul>
                                            @endif
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                                @endif
                                
                            @elseif($sectionKey === 'validasi')
                                <!-- Validasi Content -->
                                <div class="content-text">
                                    @foreach($section['content'] as $content)
                                        <p>{{ $content }}</p>
                                    @endforeach
                                </div>
                                
                                @if(isset($section['kriteria']))
                                <div class="validation-criteria">
                                    <h4>Kriteria Validasi:</h4>
                                    @foreach($section['kriteria'] as $criteria)
                                    <div class="criteria-item">
                                        <i class="fas fa-check-double"></i>
                                        <span>{{ $criteria }}</span>
                                    </div>
                                    @endforeach
                                </div>
                                @endif
                                
                            @elseif($sectionKey === 'catatan_tambahan')
                                <!-- Catatan Tambahan Content -->
                                <div class="additional-notes">
                                    @foreach($section['content'] as $note)
                                    <div class="note-item">
                                        <i class="fas fa-sticky-note"></i>
                                        <span>{{ $note }}</span>
                                    </div>
                                    @endforeach
                                </div>
                                
                            @else
                                <!-- Default Content -->
                                <div class="content-text">
                                    @if(is_array($section['content']))
                                        @foreach($section['content'] as $content)
                                            <p>{{ $content }}</p>
                                        @endforeach
                                    @else
                                        <p>{{ $section['content'] }}</p>
                                    @endif
                                </div>
                            @endif
                            
                            @if(isset($section['kesimpulan']))
                            <div class="section-conclusion">
                                <div class="conclusion-box">
                                    <h5><i class="fas fa-lightbulb"></i> Kesimpulan</h5>
                                    <p>{{ $section['kesimpulan'] }}</p>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>
                
                <!-- Sidebar -->
                <div class="col-lg-3">
                    <div class="sidebar">
                        <!-- Table of Contents -->
                        <div class="toc-card">
                            <h4>Daftar Isi</h4>
                            <ul class="toc-list">
                                @foreach($pengolahanPetaData['sections'] as $sectionKey => $section)
                                <li>
                                    <a href="#{{ $sectionKey }}">
                                        <i class="{{ $section['icon'] }}"></i>
                                        {{ $section['title'] }}
                                    </a>
                                </li>
                                @endforeach
                            </ul>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="quick-actions">
                            <h4>Aksi Cepat</h4>
                            <div class="action-buttons">
                                <a href="{{ $pengolahanPetaData['pdf_link'] }}" class="btn btn-primary btn-block" target="_blank">
                                    <i class="fas fa-download"></i> Download PDF
                                </a>
                                <a href="{{ route('materials') }}" class="btn btn-outline-secondary btn-block">
                                    <i class="fas fa-arrow-left"></i> Kembali ke Materi
                                </a>
                                <a href="{{ route('schedule') }}" class="btn btn-outline-info btn-block">
                                    <i class="fas fa-calendar"></i> Lihat Jadwal
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0;
    margin-bottom: 0;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 15px;
}

.page-description {
    font-size: 1rem;
    opacity: 0.8;
    line-height: 1.6;
}

.material-info {
    text-align: right;
}

.info-item {
    display: inline-block;
    margin: 0 15px 15px 0;
    font-size: 0.9rem;
}

.info-item i {
    margin-right: 5px;
}

.breadcrumb-section {
    background-color: #f8f9fa;
    padding: 15px 0;
    border-bottom: 1px solid #dee2e6;
}

.breadcrumb {
    background: none;
    margin: 0;
    padding: 0;
}

.content-sections {
    padding: 40px 0;
}

.content-section {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    overflow: hidden;
}

.section-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 20px 30px;
}

.section-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.section-title i {
    margin-right: 10px;
}

.section-content {
    padding: 30px;
}

.content-text p {
    line-height: 1.8;
    margin-bottom: 15px;
    color: #555;
}

.tools-materials-section {
    margin: 20px 0;
}

.tool-category {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.tool-category h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.tool-category h4 i {
    margin-right: 10px;
    color: #007bff;
}

.tool-list {
    margin: 0;
    padding-left: 20px;
}

.tool-list li {
    margin-bottom: 10px;
    line-height: 1.6;
    color: #555;
}

.preparation-steps {
    margin: 20px 0;
}

.step-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.step-item i {
    color: #28a745;
    margin-right: 15px;
    margin-top: 2px;
    font-size: 1.1rem;
}

.steps-section {
    margin-top: 25px;
}

.step-card {
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
}

.step-card .card-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 15px 20px;
    border: none;
}

.step-card .card-header h5 {
    margin: 0;
    display: inline-block;
    margin-left: 10px;
}

.step-card .card-body {
    padding: 20px;
}

.step-card ul {
    margin: 0;
    padding-left: 20px;
}

.step-card li {
    margin-bottom: 8px;
    line-height: 1.6;
}

.techniques-section {
    margin-top: 25px;
}

.techniques-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.technique-item {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border-left: 4px solid #007bff;
}

.technique-item i {
    font-size: 2rem;
    color: #007bff;
    margin-bottom: 10px;
}

.technique-item h5 {
    color: #333;
    margin-bottom: 10px;
}

.technique-item p {
    color: #555;
    margin: 0;
    line-height: 1.6;
}

.cleaning-processes {
    margin-top: 25px;
}

.process-item {
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
}

.process-header {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    color: #333;
    padding: 15px 20px;
    border: none;
}

.process-header h5 {
    margin: 0;
    display: inline-block;
    margin-left: 10px;
}

.process-content {
    padding: 20px;
}

.process-content ul {
    margin: 10px 0 0 0;
    padding-left: 20px;
}

.process-content li {
    margin-bottom: 8px;
    line-height: 1.6;
}

.validation-criteria {
    margin-top: 25px;
}

.criteria-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #17a2b8;
}

.criteria-item i {
    color: #17a2b8;
    margin-right: 15px;
    margin-top: 2px;
    font-size: 1.1rem;
}

.additional-notes {
    margin: 20px 0;
}

.note-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 15px;
    background: #fff3cd;
    border-radius: 8px;
    border-left: 4px solid #ffc107;
}

.note-item i {
    color: #ffc107;
    margin-right: 15px;
    margin-top: 2px;
    font-size: 1.1rem;
}

.section-conclusion {
    margin-top: 25px;
}

.conclusion-box {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #ff6b6b;
}

.conclusion-box h5 {
    color: #333;
    margin-bottom: 10px;
    font-weight: 600;
}

.conclusion-box h5 i {
    color: #ff6b6b;
    margin-right: 8px;
}

.conclusion-box p {
    margin: 0;
    color: #555;
    line-height: 1.6;
}

.sidebar {
    position: sticky;
    top: 20px;
}

.toc-card, .quick-actions {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 25px;
    margin-bottom: 20px;
}

.toc-card h4, .quick-actions h4 {
    margin-bottom: 20px;
    color: #333;
    font-weight: 600;
    border-bottom: 2px solid #f1f1f1;
    padding-bottom: 10px;
}

.toc-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.toc-list li {
    margin-bottom: 10px;
}

.toc-list a {
    color: #555;
    text-decoration: none;
    display: block;
    padding: 8px 12px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.toc-list a:hover {
    background: #f8f9fa;
    color: #007bff;
    transform: translateX(5px);
}

.toc-list a i {
    margin-right: 8px;
    width: 16px;
}

.action-buttons .btn {
    margin-bottom: 10px;
    border-radius: 25px;
    font-weight: 500;
    padding: 10px 20px;
}

.btn-block {
    width: 100%;
    display: block;
}

@media (max-width: 768px) {
    .page-header {
        padding: 40px 0;
        text-align: center;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .material-info {
        text-align: center;
        margin-top: 20px;
    }
    
    .section-content {
        padding: 20px;
    }
    
    .sidebar {
        margin-top: 30px;
        position: static;
    }
    
    .techniques-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
// Smooth scrolling for table of contents
document.addEventListener('DOMContentLoaded', function() {
    const tocLinks = document.querySelectorAll('.toc-list a');
    
    tocLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Update active state
                tocLinks.forEach(l => l.classList.remove('active'));
                this.classList.add('active');
            }
        });
    });
    
    // Highlight current section in TOC on scroll
    window.addEventListener('scroll', function() {
        const sections = document.querySelectorAll('.content-section');
        const scrollPos = window.scrollY + 100;
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = section.getAttribute('id');
            
            if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                tocLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === '#' + sectionId) {
                        link.classList.add('active');
                    }
                });
            }
        });
    });
});
</script>
@endsection