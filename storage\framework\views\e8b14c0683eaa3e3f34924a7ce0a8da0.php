<?php $__env->startSection('title', $pendahuluanData['title']); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title"><?php echo e($pendahuluanData['title']); ?></h1>
                    <p class="page-subtitle"><?php echo e($pendahuluanData['subtitle']); ?></p>
                    <p class="page-description"><?php echo e($pendahuluanData['description']); ?></p>
                </div>
                <div class="col-lg-4 text-end">
                    <div class="material-info">
                        <div class="info-item">
                            <i class="fas fa-clock"></i>
                            <span><?php echo e($pendahuluanData['duration']); ?></span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-signal"></i>
                            <span><?php echo e($pendahuluanData['difficulty']); ?></span>
                        </div>
                        <a href="<?php echo e($pendahuluanData['pdf_link']); ?>" class="btn btn-primary" target="_blank">
                            <i class="fas fa-download"></i> Download PDF
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Breadcrumb -->
    <div class="breadcrumb-section">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>">Beranda</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('materials')); ?>">Materi</a></li>
                    <li class="breadcrumb-item active" aria-current="page"><?php echo e($pendahuluanData['title']); ?></li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Content Sections -->
    <div class="content-sections">
        <div class="container">
            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-9">
                    <?php $__currentLoopData = $pendahuluanData['sections']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sectionKey => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="content-section" id="<?php echo e($sectionKey); ?>">
                        <div class="section-header">
                            <h2 class="section-title">
                                <i class="<?php echo e($section['icon']); ?>"></i>
                                <?php echo e($section['title']); ?>

                            </h2>
                        </div>
                        
                        <div class="section-content">
                            <?php if($sectionKey === 'latar_belakang'): ?>
                                <!-- Latar Belakang Content -->
                                <div class="content-text">
                                    <?php $__currentLoopData = $section['content']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $content): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <p><?php echo e($content); ?></p>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                
                                <?php if(isset($section['tahapan'])): ?>
                                <div class="tahapan-section">
                                    <h4>Tahapan Kegiatan:</h4>
                                    <div class="row">
                                        <?php $__currentLoopData = $section['tahapan']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tahapanKey => $tahapan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-6">
                                            <div class="tahapan-card">
                                                <div class="card-header">
                                                    <i class="<?php echo e($tahapan['icon']); ?>"></i>
                                                    <h5><?php echo e($tahapan['title']); ?></h5>
                                                </div>
                                                <div class="card-body">
                                                    <ul>
                                                        <?php $__currentLoopData = $tahapan['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <li><?php echo e($item); ?></li>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                            <?php elseif($sectionKey === 'maksud_tujuan'): ?>
                                <!-- Maksud dan Tujuan Content -->
                                <div class="objectives-list">
                                    <?php $__currentLoopData = $section['content']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $objective): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="objective-item">
                                        <i class="fas fa-check-circle"></i>
                                        <span><?php echo e($objective); ?></span>
                                    </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                
                            <?php elseif($sectionKey === 'instrumen_perangkat'): ?>
                                <!-- Instrumen dan Perangkat Content -->
                                <div class="instruments-section">
                                    <?php if(isset($section['pengolahan_master'])): ?>
                                    <div class="instrument-category">
                                        <h4>
                                            <i class="<?php echo e($section['pengolahan_master']['icon']); ?>"></i>
                                            <?php echo e($section['pengolahan_master']['title']); ?>

                                        </h4>
                                        <ul class="instrument-list">
                                            <?php $__currentLoopData = $section['pengolahan_master']['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li><?php echo e($item); ?></li>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </ul>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <?php if(isset($section['pengolahan_peta'])): ?>
                                    <div class="instrument-category">
                                        <h4>
                                            <i class="<?php echo e($section['pengolahan_peta']['icon']); ?>"></i>
                                            <?php echo e($section['pengolahan_peta']['title']); ?>

                                        </h4>
                                        <ul class="instrument-list">
                                            <?php $__currentLoopData = $section['pengolahan_peta']['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li><?php echo e($item); ?></li>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </ul>
                                    </div>
                                    <?php endif; ?>
                                    
                                    <?php if(isset($section['software_aplikasi'])): ?>
                                    <div class="instrument-category">
                                        <h4>
                                            <i class="<?php echo e($section['software_aplikasi']['icon']); ?>"></i>
                                            <?php echo e($section['software_aplikasi']['title']); ?>

                                        </h4>
                                        <ul class="instrument-list">
                                            <?php $__currentLoopData = $section['software_aplikasi']['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li><?php echo e($item); ?></li>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </ul>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                
                            <?php elseif($sectionKey === 'jadwal_kegiatan'): ?>
                                <!-- Jadwal Kegiatan Content -->
                                <div class="content-text">
                                    <p><?php echo e($section['content']); ?></p>
                                    <?php if(isset($section['reference_link'])): ?>
                                    <div class="reference-link">
                                        <a href="<?php echo e($section['reference_link']); ?>" class="btn btn-outline-primary">
                                            <i class="fas fa-external-link-alt"></i>
                                            <?php echo e($section['reference_text']); ?>

                                        </a>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                
                            <?php else: ?>
                                <!-- Default Content -->
                                <div class="content-text">
                                    <p><?php echo e($section['content']); ?></p>
                                </div>
                            <?php endif; ?>
                            
                            <?php if(isset($section['kesimpulan'])): ?>
                            <div class="section-conclusion">
                                <div class="conclusion-box">
                                    <h5><i class="fas fa-lightbulb"></i> Kesimpulan</h5>
                                    <p><?php echo e($section['kesimpulan']); ?></p>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                
                <!-- Sidebar -->
                <div class="col-lg-3">
                    <div class="sidebar">
                        <!-- Table of Contents -->
                        <div class="toc-card">
                            <h4>Daftar Isi</h4>
                            <ul class="toc-list">
                                <?php $__currentLoopData = $pendahuluanData['sections']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sectionKey => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li>
                                    <a href="#<?php echo e($sectionKey); ?>">
                                        <i class="<?php echo e($section['icon']); ?>"></i>
                                        <?php echo e($section['title']); ?>

                                    </a>
                                </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="quick-actions">
                            <h4>Aksi Cepat</h4>
                            <div class="action-buttons">
                                <a href="<?php echo e($pendahuluanData['pdf_link']); ?>" class="btn btn-primary btn-block" target="_blank">
                                    <i class="fas fa-download"></i> Download PDF
                                </a>
                                <a href="<?php echo e(route('materials')); ?>" class="btn btn-outline-secondary btn-block">
                                    <i class="fas fa-arrow-left"></i> Kembali ke Materi
                                </a>
                                <a href="<?php echo e(route('schedule')); ?>" class="btn btn-outline-info btn-block">
                                    <i class="fas fa-calendar"></i> Lihat Jadwal
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 60px 0;
    margin-bottom: 0;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 15px;
}

.page-description {
    font-size: 1rem;
    opacity: 0.8;
    line-height: 1.6;
}

.material-info {
    text-align: right;
}

.info-item {
    display: inline-block;
    margin: 0 15px 15px 0;
    font-size: 0.9rem;
}

.info-item i {
    margin-right: 5px;
}

.breadcrumb-section {
    background-color: #f8f9fa;
    padding: 15px 0;
    border-bottom: 1px solid #dee2e6;
}

.breadcrumb {
    background: none;
    margin: 0;
    padding: 0;
}

.content-sections {
    padding: 40px 0;
}

.content-section {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    overflow: hidden;
}

.section-header {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 20px 30px;
}

.section-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.section-title i {
    margin-right: 10px;
}

.section-content {
    padding: 30px;
}

.content-text p {
    line-height: 1.8;
    margin-bottom: 15px;
    color: #555;
}

.tahapan-section {
    margin-top: 25px;
}

.tahapan-card {
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
    overflow: hidden;
}

.tahapan-card .card-header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 15px 20px;
    border: none;
}

.tahapan-card .card-header h5 {
    margin: 0;
    display: inline-block;
    margin-left: 10px;
}

.tahapan-card .card-body {
    padding: 20px;
}

.tahapan-card ul {
    margin: 0;
    padding-left: 20px;
}

.tahapan-card li {
    margin-bottom: 8px;
    line-height: 1.6;
}

.objectives-list {
    margin: 20px 0;
}

.objective-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.objective-item i {
    color: #28a745;
    margin-right: 15px;
    margin-top: 2px;
    font-size: 1.1rem;
}

.instruments-section {
    margin: 20px 0;
}

.instrument-category {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.instrument-category h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.instrument-category h4 i {
    margin-right: 10px;
    color: #007bff;
}

.instrument-list {
    margin: 0;
    padding-left: 20px;
}

.instrument-list li {
    margin-bottom: 10px;
    line-height: 1.6;
    color: #555;
}

.reference-link {
    margin-top: 20px;
}

.section-conclusion {
    margin-top: 25px;
}

.conclusion-box {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #ff6b6b;
}

.conclusion-box h5 {
    color: #333;
    margin-bottom: 10px;
    font-weight: 600;
}

.conclusion-box h5 i {
    color: #ff6b6b;
    margin-right: 8px;
}

.conclusion-box p {
    margin: 0;
    color: #555;
    line-height: 1.6;
}

.sidebar {
    position: sticky;
    top: 20px;
}

.toc-card, .quick-actions {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 25px;
    margin-bottom: 20px;
}

.toc-card h4, .quick-actions h4 {
    margin-bottom: 20px;
    color: #333;
    font-weight: 600;
    border-bottom: 2px solid #f1f1f1;
    padding-bottom: 10px;
}

.toc-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.toc-list li {
    margin-bottom: 10px;
}

.toc-list a {
    color: #555;
    text-decoration: none;
    display: block;
    padding: 8px 12px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.toc-list a:hover {
    background: #f8f9fa;
    color: #007bff;
    transform: translateX(5px);
}

.toc-list a i {
    margin-right: 8px;
    width: 16px;
}

.action-buttons .btn {
    margin-bottom: 10px;
    border-radius: 25px;
    font-weight: 500;
    padding: 10px 20px;
}

.btn-block {
    width: 100%;
    display: block;
}

@media (max-width: 768px) {
    .page-header {
        padding: 40px 0;
        text-align: center;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .material-info {
        text-align: center;
        margin-top: 20px;
    }
    
    .section-content {
        padding: 20px;
    }
    
    .sidebar {
        margin-top: 30px;
        position: static;
    }
}
</style>

<script>
// Smooth scrolling for table of contents
document.addEventListener('DOMContentLoaded', function() {
    const tocLinks = document.querySelectorAll('.toc-list a');
    
    tocLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // Update active state
                tocLinks.forEach(l => l.classList.remove('active'));
                this.classList.add('active');
            }
        });
    });
    
    // Highlight current section in TOC on scroll
    window.addEventListener('scroll', function() {
        const sections = document.querySelectorAll('.content-section');
        const scrollPos = window.scrollY + 100;
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.offsetHeight;
            const sectionId = section.getAttribute('id');
            
            if (scrollPos >= sectionTop && scrollPos < sectionTop + sectionHeight) {
                tocLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === '#' + sectionId) {
                        link.classList.add('active');
                    }
                });
            }
        });
    });
});
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\pengolahanwilker\resources\views/training/pendahuluan.blade.php ENDPATH**/ ?>