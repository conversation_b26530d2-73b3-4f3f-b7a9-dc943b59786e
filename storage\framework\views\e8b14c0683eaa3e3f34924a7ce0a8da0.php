<?php $__env->startSection('title', $pendahuluanData['title']); ?>

<?php $__env->startSection('head'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/training-modern.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Modern Page Header -->
    <div class="modern-page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="modern-page-title"><?php echo e($pendahuluanData['title']); ?></h1>
                    <p class="modern-page-subtitle"><?php echo e($pendahuluanData['subtitle']); ?></p>
                    <p class="modern-page-description"><?php echo e($pendahuluanData['description']); ?></p>
                </div>
                <div class="col-lg-4">
                    <div class="modern-material-info mt-4">
                        
                        <a href="<?php echo e($pendahuluanData['pdf_link']); ?>" class="modern-download-btn" target="_blank">
                            <i class="fas fa-download"></i>
                            Download PDF
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Breadcrumb -->
    <div class="modern-breadcrumb-section">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="modern-breadcrumb">
                    <li class="modern-breadcrumb-item">
                        <a href="<?php echo e(route('home')); ?>" class="modern-breadcrumb-link">Beranda</a>
                    </li>
                    <li class="modern-breadcrumb-item">
                        <a href="<?php echo e(route('materials')); ?>" class="modern-breadcrumb-link">Materi</a>
                    </li>
                    <li class="modern-breadcrumb-item">
                        <span class="modern-breadcrumb-current"><?php echo e($pendahuluanData['title']); ?></span>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Modern Content Layout -->
    <div class="modern-content-layout">
        <div class="container">
            <div class="modern-content-grid">
                <!-- Main Content -->
                <div class="main-content">
                    <div class="modern-accordion">
                        <?php $__currentLoopData = $pendahuluanData['sections']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sectionKey => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="modern-accordion-item" id="<?php echo e($sectionKey); ?>">
                            <button class="modern-accordion-header">
                                <div class="modern-accordion-title">
                                    <i class="modern-accordion-icon <?php echo e($section['icon']); ?>"></i>
                                    <?php echo e($section['title']); ?>

                                </div>
                                <i class="modern-accordion-chevron fas fa-chevron-down"></i>
                            </button>

                            <div class="modern-accordion-content">
                                <div class="modern-accordion-body">
                                    <?php if($sectionKey === 'latar_belakang'): ?>
                                        <!-- Latar Belakang Content -->
                                        <div class="mb-6">
                                            <?php $__currentLoopData = $section['content']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $content): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <p class="mb-4"><?php echo e($content); ?></p>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>

                                        <?php if(isset($section['tahapan'])): ?>
                                        <div class="mb-6">
                                            <h4 class="mb-4 text-xl font-semibold text-gray-800">Tahapan Kegiatan:</h4>
                                            <div class="modern-feature-grid">
                                                <?php $__currentLoopData = $section['tahapan']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tahapanKey => $tahapan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="modern-feature-card">
                                                    <div class="modern-feature-icon">
                                                        <i class="<?php echo e($tahapan['icon']); ?>"></i>
                                                    </div>
                                                    <h5 class="modern-feature-title"><?php echo e($tahapan['title']); ?></h5>
                                                    <ul class="modern-list">
                                                        <?php $__currentLoopData = $tahapan['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <li class="modern-list-item">
                                                            <div class="modern-list-icon">
                                                                <i class="fas fa-check"></i>
                                                            </div>
                                                            <div class="modern-list-content"><?php echo e($item); ?></div>
                                                        </li>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </ul>
                                                </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                
                                    <?php elseif($sectionKey === 'maksud_tujuan'): ?>
                                        <!-- Maksud dan Tujuan Content -->
                                        <div class="mb-6">
                                            <ul class="modern-list">
                                                <?php $__currentLoopData = $section['content']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $objective): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <li class="modern-list-item">
                                                    <div class="modern-list-icon">
                                                        <i class="fas fa-bullseye"></i>
                                                    </div>
                                                    <div class="modern-list-content"><?php echo e($objective); ?></div>
                                                </li>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </ul>
                                        </div>

                                    <?php elseif($sectionKey === 'instrumen_perangkat'): ?>
                                        <!-- Instrumen dan Perangkat Content -->
                                        <div class="modern-feature-grid">
                                            <?php if(isset($section['pengolahan_master'])): ?>
                                            <div class="modern-feature-card">
                                                <div class="modern-feature-icon">
                                                    <i class="<?php echo e($section['pengolahan_master']['icon']); ?>"></i>
                                                </div>
                                                <h5 class="modern-feature-title"><?php echo e($section['pengolahan_master']['title']); ?></h5>
                                                <ul class="modern-list">
                                                    <?php $__currentLoopData = $section['pengolahan_master']['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <li class="modern-list-item">
                                                        <div class="modern-list-icon">
                                                            <i class="fas fa-cog"></i>
                                                        </div>
                                                        <div class="modern-list-content"><?php echo e($item); ?></div>
                                                    </li>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </ul>
                                            </div>
                                            <?php endif; ?>

                                            <?php if(isset($section['pengolahan_peta'])): ?>
                                            <div class="modern-feature-card">
                                                <div class="modern-feature-icon">
                                                    <i class="<?php echo e($section['pengolahan_peta']['icon']); ?>"></i>
                                                </div>
                                                <h5 class="modern-feature-title"><?php echo e($section['pengolahan_peta']['title']); ?></h5>
                                                <ul class="modern-list">
                                                    <?php $__currentLoopData = $section['pengolahan_peta']['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <li class="modern-list-item">
                                                        <div class="modern-list-icon">
                                                            <i class="fas fa-map"></i>
                                                        </div>
                                                        <div class="modern-list-content"><?php echo e($item); ?></div>
                                                    </li>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </ul>
                                            </div>
                                            <?php endif; ?>

                                            <?php if(isset($section['software_aplikasi'])): ?>
                                            <div class="modern-feature-card">
                                                <div class="modern-feature-icon">
                                                    <i class="<?php echo e($section['software_aplikasi']['icon']); ?>"></i>
                                                </div>
                                                <h5 class="modern-feature-title"><?php echo e($section['software_aplikasi']['title']); ?></h5>
                                                <ul class="modern-list">
                                                    <?php $__currentLoopData = $section['software_aplikasi']['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <li class="modern-list-item">
                                                        <div class="modern-list-icon">
                                                            <i class="fas fa-laptop-code"></i>
                                                        </div>
                                                        <div class="modern-list-content"><?php echo e($item); ?></div>
                                                    </li>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </ul>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                
                                    <?php elseif($sectionKey === 'jadwal_kegiatan'): ?>
                                        <!-- Jadwal Kegiatan Content -->
                                        <div class="mb-6">
                                            <p class="mb-4"><?php echo e($section['content']); ?></p>
                                            <?php if(isset($section['reference_link'])): ?>
                                            <div class="modern-alert modern-alert-info">
                                                <div class="modern-alert-icon">
                                                    <i class="fas fa-info-circle"></i>
                                                </div>
                                                <div class="modern-alert-content">
                                                    <p class="mb-3">Untuk informasi lebih detail, silakan kunjungi:</p>
                                                    <a href="<?php echo e($section['reference_link']); ?>" class="modern-btn modern-btn-outline">
                                                        <i class="fas fa-external-link-alt"></i>
                                                        <?php echo e($section['reference_text']); ?>

                                                    </a>
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>

                                    <?php else: ?>
                                        <!-- Default Content -->
                                        <div class="mb-6">
                                            <p><?php echo e($section['content']); ?></p>
                                        </div>
                                    <?php endif; ?>

                                    <?php if(isset($section['kesimpulan'])): ?>
                                    <div class="modern-alert modern-alert-success">
                                        <div class="modern-alert-icon">
                                            <i class="fas fa-lightbulb"></i>
                                        </div>
                                        <div class="modern-alert-content">
                                            <h6 class="font-semibold mb-2">Kesimpulan</h6>
                                            <p class="mb-0"><?php echo e($section['kesimpulan']); ?></p>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                
                <!-- Modern Sidebar -->
                <div class="modern-sidebar">
                    <!-- Table of Contents -->
                    <div class="modern-sidebar-card">
                        <h4 class="modern-sidebar-title">Daftar Isi</h4>
                        <ul class="modern-toc">
                            <?php $__currentLoopData = $pendahuluanData['sections']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sectionKey => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="modern-toc-item">
                                <a href="#<?php echo e($sectionKey); ?>" class="modern-toc-link">
                                    <i class="modern-toc-icon <?php echo e($section['icon']); ?>"></i>
                                    <?php echo e($section['title']); ?>

                                </a>
                            </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>

                    <!-- Quick Actions -->
                    <div class="modern-sidebar-card">
                        <h4 class="modern-sidebar-title">Aksi Cepat</h4>
                        <div class="modern-action-buttons">
                            <a href="<?php echo e($pendahuluanData['pdf_link']); ?>" class="modern-btn modern-btn-primary" target="_blank">
                                <i class="fas fa-download"></i>
                                Download PDF
                            </a>
                            <a href="<?php echo e(route('materials')); ?>" class="modern-btn modern-btn-secondary">
                                <i class="fas fa-arrow-left"></i>
                                Kembali ke Materi
                            </a>
                            <a href="<?php echo e(route('schedule')); ?>" class="modern-btn modern-btn-outline">
                                <i class="fas fa-calendar"></i>
                                Lihat Jadwal
                            </a>
                        </div>
                    </div>

                    <!-- Progress Card -->
                    <div class="modern-sidebar-card">
                        <h4 class="modern-sidebar-title">Progress Pembelajaran</h4>
                        <div class="modern-progress-card">
                            <div class="modern-progress-header">
                                <div class="modern-progress-number">1</div>
                                <h5 class="modern-progress-title">Pendahuluan</h5>
                            </div>
                            <div class="modern-progress-content">
                                Memahami dasar-dasar pengolahan wilkerstat dan persiapan yang diperlukan.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="<?php echo e(asset('js/training-modern.js')); ?>"></script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\pengolahanwilker\resources\views/training/pendahuluan.blade.php ENDPATH**/ ?>