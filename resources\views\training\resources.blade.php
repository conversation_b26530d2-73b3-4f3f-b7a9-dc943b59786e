@extends('layouts.app')

@section('title', 'Sumber <PERSON> - <PERSON><PERSON><PERSON><PERSON>ahan Wilkerstat SE2026')

@section('content')
<!-- Page Header -->
<div class="page-header mb-8">
    <div class="page-header-content">
        <div class="page-title-section">
            <h1 class="page-title">
                <i class="fas fa-external-link-alt"></i>
                Sumber Daya Tambahan
            </h1>
            <p class="page-subtitle">
                Koleksi lengkap tools, aplikasi, dan sumber daya eksternal untuk mendukung pengolahan Wilkerstat SE2026.
            </p>
        </div>
        <div class="page-actions">
            <button class="btn btn-secondary" onclick="exportBookmarks()">
                <i class="fas fa-bookmark"></i>
                Ekspor Bookmark
            </button>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="stats-section mb-8">
    <div class="grid grid-cols-1 grid-cols-md-4">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-tools"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">{{ collect($resources)->flatten(1)->where('type', 'Tool')->count() }}</div>
                <div class="stat-label">Tools & Software</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-database"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">{{ collect($resources)->flatten(1)->where('type', 'Database')->count() }}</div>
                <div class="stat-label">Database & API</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-book"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">{{ collect($resources)->flatten(1)->where('type', 'Documentation')->count() }}</div>
                <div class="stat-label">Dokumentasi</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number">{{ collect($resources)->flatten(1)->where('type', 'Learning')->count() }}</div>
                <div class="stat-label">Pembelajaran</div>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="search-section mb-6">
    <div class="search-card">
        <div class="search-controls">
            <div class="search-input-group">
                <i class="fas fa-search search-icon"></i>
                <input type="text" id="searchInput" class="search-input" placeholder="Cari sumber daya berdasarkan nama atau deskripsi...">
            </div>
            <div class="filter-group">
                <select id="categoryFilter" class="filter-select">
                    <option value="">Semua Kategori</option>
                    @foreach($resources as $category => $items)
                    <option value="{{ strtolower($category) }}">{{ $category }}</option>
                    @endforeach
                </select>
            </div>
            <div class="filter-group">
                <select id="typeFilter" class="filter-select">
                    <option value="">Semua Tipe</option>
                    <option value="Tool">Tool</option>
                    <option value="Database">Database</option>
                    <option value="Documentation">Dokumentasi</option>
                    <option value="Learning">Pembelajaran</option>
                </select>
            </div>
            <div class="filter-group">
                <button class="btn btn-primary" onclick="resetFilters()">
                    <i class="fas fa-refresh"></i>
                    Reset
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Resources by Category -->
<div class="resources-section">
    @foreach($resources as $categoryName => $categoryItems)
    <div class="category-section mb-8" data-category="{{ strtolower($categoryName) }}">
        <div class="category-header">
            <div class="category-title-section">
                <h2 class="category-title">
                    <i class="fas fa-folder-open"></i>
                    {{ $categoryName }}
                </h2>
                <div class="category-stats">
                    <span class="category-count">{{ count($categoryItems) }} sumber daya</span>
                    <div class="category-types">
                        @php
                            $types = collect($categoryItems)->pluck('type')->unique();
                        @endphp
                        @foreach($types as $type)
                            <span class="type-badge type-{{ strtolower($type) }}">{{ $type }}</span>
                        @endforeach
                    </div>
                </div>
            </div>
            <div class="category-actions">
                <button class="btn btn-outline btn-sm" onclick="toggleCategory('{{ $loop->index }}')">
                    <i class="fas fa-chevron-down toggle-icon" id="categoryToggle-{{ $loop->index }}"></i>
                </button>
            </div>
        </div>
        
        <div class="category-content" id="categoryContent-{{ $loop->index }}">
            <div class="resources-grid">
                @foreach($categoryItems as $resource)
                <div class="resource-card" data-name="{{ strtolower($resource['name']) }}" data-description="{{ strtolower($resource['description']) }}" data-type="{{ $resource['type'] }}">
                    <div class="resource-header">
                        <div class="resource-icon">
                            <i class="{{ $resource['icon'] }}"></i>
                        </div>
                        <div class="resource-type-badge type-{{ strtolower($resource['type']) }}">
                            {{ $resource['type'] }}
                        </div>
                        <div class="resource-actions">
                            <button class="btn-action btn-bookmark" onclick="toggleBookmark(this, '{{ $resource['name'] }}')" title="Bookmark">
                                <i class="far fa-bookmark"></i>
                            </button>
                            <button class="btn-action btn-share" onclick="shareResource('{{ $resource['name'] }}', '{{ $resource['link'] }}')" title="Bagikan">
                                <i class="fas fa-share-alt"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="resource-content">
                        <h3 class="resource-title">{{ $resource['name'] }}</h3>
                        <p class="resource-description">{{ $resource['description'] }}</p>
                        
                        @if(isset($resource['features']) && count($resource['features']) > 0)
                        <div class="resource-features">
                            <h5>Fitur Utama:</h5>
                            <ul class="features-list">
                                @foreach(array_slice($resource['features'], 0, 3) as $feature)
                                <li>{{ $feature }}</li>
                                @endforeach
                                @if(count($resource['features']) > 3)
                                <li class="more-features">+{{ count($resource['features']) - 3 }} fitur lainnya</li>
                                @endif
                            </ul>
                        </div>
                        @endif
                        
                        <div class="resource-meta">
                            @if(isset($resource['platform']))
                            <div class="meta-item">
                                <i class="fas fa-desktop"></i>
                                <span>{{ $resource['platform'] }}</span>
                            </div>
                            @endif
                            @if(isset($resource['price']))
                            <div class="meta-item">
                                <i class="fas fa-tag"></i>
                                <span>{{ $resource['price'] }}</span>
                            </div>
                            @endif
                            @if(isset($resource['language']))
                            <div class="meta-item">
                                <i class="fas fa-language"></i>
                                <span>{{ $resource['language'] }}</span>
                            </div>
                            @endif
                        </div>
                    </div>
                    
                    <div class="resource-footer">
                        <a href="{{ $resource['link'] }}" class="btn btn-primary btn-block" target="_blank" onclick="trackClick('{{ $resource['name'] }}')">
                            <i class="fas fa-external-link-alt"></i>
                            Buka {{ $resource['type'] }}
                        </a>
                        <button class="btn btn-outline btn-sm" onclick="showResourceDetails('{{ $resource['name'] }}', {{ json_encode($resource) }})">
                            <i class="fas fa-info-circle"></i>
                            Detail
                        </button>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
    </div>
    @endforeach
</div>

<!-- No Results -->
<div class="no-results" id="noResults" style="display: none;">
    <div class="no-results-content">
        <i class="fas fa-search"></i>
        <h3>Tidak ada sumber daya ditemukan</h3>
        <p>Coba ubah kata kunci pencarian atau filter Anda</p>
        <button class="btn btn-primary" onclick="resetFilters()">
            <i class="fas fa-refresh"></i>
            Reset Filter
        </button>
    </div>
</div>

<!-- Resource Details Modal -->
<div class="modal" id="resourceModal">
    <div class="modal-content modal-large">
        <div class="modal-header">
            <h3 class="modal-title" id="resourceModalTitle">Detail Sumber Daya</h3>
            <button class="modal-close" onclick="closeResourceModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body" id="resourceModalBody">
            <!-- Content will be populated by JavaScript -->
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeResourceModal()">Tutup</button>
            <button class="btn btn-primary" id="resourceModalLink">
                <i class="fas fa-external-link-alt"></i>
                Buka Sumber Daya
            </button>
        </div>
    </div>
</div>

<!-- Bookmarks Sidebar -->
<div class="bookmarks-sidebar" id="bookmarksSidebar">
    <div class="sidebar-header">
        <h4>Bookmark Saya</h4>
        <button class="sidebar-toggle" onclick="toggleBookmarksSidebar()">
            <i class="fas fa-chevron-left"></i>
        </button>
    </div>
    <div class="sidebar-content">
        <div class="bookmarks-list" id="bookmarksList">
            <p class="empty-state">Belum ada bookmark</p>
        </div>
        <div class="sidebar-actions">
            <button class="btn btn-outline btn-sm btn-block" onclick="clearAllBookmarks()">
                <i class="fas fa-trash"></i>
                Hapus Semua
            </button>
        </div>
    </div>
</div>

<!-- Quick Access Floating Button -->
<div class="quick-access-fab">
    <button class="fab" onclick="toggleBookmarksSidebar()" title="Bookmark Saya">
        <i class="fas fa-bookmark"></i>
        <span class="bookmark-count" id="bookmarkCount">0</span>
    </button>
</div>
@endsection

@push('styles')
<style>
    /* Page Header */
    .page-header {
        background: linear-gradient(135deg, var(--bg-white) 0%, #f8fafc 100%);
        border-radius: 0.75rem;
        padding: 2rem;
        border: 1px solid var(--border-color);
    }

    .page-header-content {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 2rem;
    }

    .page-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .page-title i {
        color: var(--primary-color);
    }

    .page-subtitle {
        color: var(--text-secondary);
        font-size: 1.125rem;
        line-height: 1.6;
    }

    /* Stats Cards */
    .stat-card {
        background: var(--bg-white);
        border-radius: 0.75rem;
        padding: 1.5rem;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
        display: flex;
        align-items: center;
        gap: 1rem;
        transition: all 0.3s ease;
    }

    .stat-card:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        border-radius: 0.5rem;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
    }

    .stat-number {
        font-size: 1.75rem;
        font-weight: 700;
        color: var(--text-primary);
        line-height: 1;
    }

    .stat-label {
        color: var(--text-secondary);
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Search Section */
    .search-card {
        background: var(--bg-white);
        border-radius: 0.75rem;
        padding: 1.5rem;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
    }

    .search-controls {
        display: grid;
        grid-template-columns: 1fr auto auto auto;
        gap: 1rem;
        align-items: center;
    }

    .search-input-group {
        position: relative;
    }

    .search-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--text-secondary);
    }

    .search-input {
        width: 100%;
        padding: 0.75rem 1rem 0.75rem 2.5rem;
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        font-size: 0.875rem;
        transition: all 0.3s ease;
    }

    .search-input:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .filter-select {
        padding: 0.75rem 1rem;
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        font-size: 0.875rem;
        background: var(--bg-white);
        min-width: 150px;
    }

    /* Category Sections */
    .category-section {
        background: var(--bg-white);
        border-radius: 0.75rem;
        border: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
        overflow: hidden;
    }

    .category-header {
        padding: 1.5rem;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .category-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 0.5rem;
    }

    .category-title i {
        color: var(--primary-color);
    }

    .category-stats {
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .category-count {
        font-size: 0.875rem;
        color: var(--text-secondary);
        background: var(--bg-white);
        padding: 0.25rem 0.75rem;
        border-radius: 9999px;
    }

    .category-types {
        display: flex;
        gap: 0.5rem;
    }

    .type-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-weight: 500;
        color: white;
    }

    .type-tool { background: #3b82f6; }
    .type-database { background: #10b981; }
    .type-documentation { background: #f59e0b; }
    .type-learning { background: #8b5cf6; }

    .category-content {
        padding: 1.5rem;
        transition: all 0.3s ease;
    }

    .category-content.collapsed {
        display: none;
    }

    .toggle-icon {
        transition: transform 0.3s ease;
    }

    .toggle-icon.rotated {
        transform: rotate(180deg);
    }

    /* Resources Grid */
    .resources-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
    }

    /* Resource Cards */
    .resource-card {
        background: var(--bg-white);
        border: 1px solid var(--border-color);
        border-radius: 0.75rem;
        overflow: hidden;
        transition: all 0.3s ease;
        position: relative;
    }

    .resource-card:hover {
        box-shadow: var(--shadow-md);
        transform: translateY(-2px);
        border-color: var(--primary-color);
    }

    .resource-header {
        padding: 1.5rem;
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        display: flex;
        align-items: center;
        gap: 1rem;
        position: relative;
    }

    .resource-icon {
        width: 50px;
        height: 50px;
        border-radius: 0.5rem;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
        flex-shrink: 0;
    }

    .resource-type-badge {
        position: absolute;
        top: 1rem;
        right: 4rem;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-weight: 500;
        color: white;
    }

    .resource-actions {
        position: absolute;
        top: 1rem;
        right: 1rem;
        display: flex;
        gap: 0.5rem;
    }

    .btn-action {
        width: 32px;
        height: 32px;
        border-radius: 0.375rem;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: var(--bg-white);
        color: var(--text-secondary);
        box-shadow: var(--shadow-sm);
    }

    .btn-bookmark:hover {
        color: #f59e0b;
        background: #fffbeb;
    }

    .btn-bookmark.active {
        color: #f59e0b;
        background: #fffbeb;
    }

    .btn-bookmark.active i {
        font-weight: 900;
    }

    .btn-share:hover {
        color: var(--primary-color);
        background: #eff6ff;
    }

    .resource-content {
        padding: 1.5rem;
    }

    .resource-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.75rem;
        line-height: 1.4;
    }

    .resource-description {
        color: var(--text-secondary);
        font-size: 0.875rem;
        line-height: 1.5;
        margin-bottom: 1rem;
    }

    .resource-features {
        margin-bottom: 1rem;
    }

    .resource-features h5 {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
    }

    .features-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .features-list li {
        font-size: 0.75rem;
        color: var(--text-secondary);
        margin-bottom: 0.25rem;
        padding-left: 1rem;
        position: relative;
    }

    .features-list li:before {
        content: '•';
        color: var(--primary-color);
        position: absolute;
        left: 0;
    }

    .more-features {
        font-style: italic;
        color: var(--primary-color) !important;
    }

    .resource-meta {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .meta-item {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.75rem;
        color: var(--text-secondary);
    }

    .meta-item i {
        color: var(--primary-color);
    }

    .resource-footer {
        padding: 1.5rem;
        border-top: 1px solid var(--border-color);
        display: flex;
        gap: 0.5rem;
    }

    .btn-block {
        flex: 1;
    }

    /* Bookmarks Sidebar */
    .bookmarks-sidebar {
        position: fixed;
        top: 0;
        right: -350px;
        width: 350px;
        height: 100vh;
        background: var(--bg-white);
        border-left: 1px solid var(--border-color);
        box-shadow: var(--shadow-lg);
        transition: right 0.3s ease;
        z-index: 1000;
        display: flex;
        flex-direction: column;
    }

    .bookmarks-sidebar.open {
        right: 0;
    }

    .sidebar-header {
        padding: 1.5rem;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
    }

    .sidebar-header h4 {
        margin: 0;
        font-size: 1.125rem;
        font-weight: 600;
    }

    .sidebar-toggle {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        width: 32px;
        height: 32px;
        border-radius: 0.375rem;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .sidebar-toggle:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    .sidebar-content {
        flex: 1;
        padding: 1.5rem;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
    }

    .bookmarks-list {
        flex: 1;
        margin-bottom: 1rem;
    }

    .bookmark-item {
        padding: 1rem;
        border: 1px solid var(--border-color);
        border-radius: 0.5rem;
        margin-bottom: 0.75rem;
        background: var(--bg-white);
        transition: all 0.3s ease;
    }

    .bookmark-item:hover {
        box-shadow: var(--shadow-sm);
        border-color: var(--primary-color);
    }

    .bookmark-title {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 0.25rem;
        font-size: 0.875rem;
    }

    .bookmark-type {
        font-size: 0.75rem;
        color: var(--text-secondary);
        margin-bottom: 0.5rem;
    }

    .bookmark-actions {
        display: flex;
        gap: 0.5rem;
    }

    .bookmark-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }

    .empty-state {
        text-align: center;
        color: var(--text-secondary);
        font-style: italic;
        padding: 2rem 0;
    }

    /* Quick Access FAB */
    .quick-access-fab {
        position: fixed;
        bottom: 2rem;
        right: 2rem;
        z-index: 999;
    }

    .fab {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        border: none;
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        color: white;
        box-shadow: var(--shadow-lg);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
        transition: all 0.3s ease;
        position: relative;
    }

    .fab:hover {
        transform: scale(1.1);
    }

    .bookmark-count {
        position: absolute;
        top: -8px;
        right: -8px;
        background: #ef4444;
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.75rem;
        font-weight: 600;
    }

    .bookmark-count.hidden {
        display: none;
    }

    /* Modal */
    .modal-large .modal-content {
        max-width: 800px;
    }

    /* No Results */
    .no-results {
        padding: 3rem;
        text-align: center;
        background: var(--bg-white);
        border-radius: 0.75rem;
        border: 1px solid var(--border-color);
    }

    .no-results-content i {
        font-size: 3rem;
        color: var(--text-secondary);
        margin-bottom: 1rem;
    }

    .no-results-content h3 {
        color: var(--text-primary);
        margin-bottom: 0.5rem;
    }

    .no-results-content p {
        color: var(--text-secondary);
        margin-bottom: 1.5rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .page-header-content {
            flex-direction: column;
            align-items: stretch;
        }

        .search-controls {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .category-header {
            flex-direction: column;
            align-items: stretch;
            gap: 1rem;
        }

        .category-actions {
            justify-content: flex-end;
        }

        .resources-grid {
            grid-template-columns: 1fr;
        }

        .grid-cols-md-4 {
            grid-template-columns: repeat(2, 1fr);
        }

        .bookmarks-sidebar {
            width: 100vw;
            right: -100vw;
        }

        .quick-access-fab {
            bottom: 1rem;
            right: 1rem;
        }
    }

    @media (max-width: 480px) {
        .grid-cols-md-4 {
            grid-template-columns: 1fr;
        }

        .resource-footer {
            flex-direction: column;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    let bookmarks = JSON.parse(localStorage.getItem('resourceBookmarks') || '[]');
    let clickStats = JSON.parse(localStorage.getItem('resourceClicks') || '{}');

    // Initialize page
    document.addEventListener('DOMContentLoaded', function() {
        updateBookmarkCount();
        updateBookmarksList();
        initializeBookmarks();
        
        // Search and filter event listeners
        document.getElementById('searchInput').addEventListener('input', filterResources);
        document.getElementById('categoryFilter').addEventListener('change', filterResources);
        document.getElementById('typeFilter').addEventListener('change', filterResources);
    });

    // Filter resources
    function filterResources() {
        const searchTerm = document.getElementById('searchInput').value.toLowerCase();
        const categoryFilter = document.getElementById('categoryFilter').value;
        const typeFilter = document.getElementById('typeFilter').value;
        
        const resourceCards = document.querySelectorAll('.resource-card');
        const categorySections = document.querySelectorAll('.category-section');
        
        let hasVisibleResults = false;
        
        categorySections.forEach(section => {
            let hasVisibleCards = false;
            const sectionCategory = section.dataset.category;
            const cards = section.querySelectorAll('.resource-card');
            
            // Check category filter
            const matchesCategory = !categoryFilter || sectionCategory === categoryFilter;
            
            if (matchesCategory) {
                cards.forEach(card => {
                    const name = card.dataset.name;
                    const description = card.dataset.description;
                    const type = card.dataset.type;
                    
                    const matchesSearch = !searchTerm || name.includes(searchTerm) || description.includes(searchTerm);
                    const matchesType = !typeFilter || type === typeFilter;
                    
                    if (matchesSearch && matchesType) {
                        card.style.display = 'block';
                        hasVisibleCards = true;
                        hasVisibleResults = true;
                    } else {
                        card.style.display = 'none';
                    }
                });
            }
            
            section.style.display = (matchesCategory && hasVisibleCards) ? 'block' : 'none';
        });
        
        document.getElementById('noResults').style.display = hasVisibleResults ? 'none' : 'block';
        document.querySelector('.resources-section').style.display = hasVisibleResults ? 'block' : 'none';
    }

    // Reset filters
    function resetFilters() {
        document.getElementById('searchInput').value = '';
        document.getElementById('categoryFilter').value = '';
        document.getElementById('typeFilter').value = '';
        filterResources();
    }

    // Toggle category
    function toggleCategory(index) {
        const content = document.getElementById(`categoryContent-${index}`);
        const icon = document.getElementById(`categoryToggle-${index}`);
        
        content.classList.toggle('collapsed');
        icon.classList.toggle('rotated');
    }

    // Toggle bookmark
    function toggleBookmark(button, resourceName) {
        const icon = button.querySelector('i');
        
        if (bookmarks.includes(resourceName)) {
            // Remove bookmark
            bookmarks = bookmarks.filter(name => name !== resourceName);
            icon.className = 'far fa-bookmark';
            button.classList.remove('active');
        } else {
            // Add bookmark
            bookmarks.push(resourceName);
            icon.className = 'fas fa-bookmark';
            button.classList.add('active');
        }
        
        localStorage.setItem('resourceBookmarks', JSON.stringify(bookmarks));
        updateBookmarkCount();
        updateBookmarksList();
    }

    // Initialize bookmarks
    function initializeBookmarks() {
        bookmarks.forEach(resourceName => {
            const cards = document.querySelectorAll('.resource-card');
            cards.forEach(card => {
                const cardName = card.querySelector('.resource-title').textContent;
                if (cardName === resourceName) {
                    const button = card.querySelector('.btn-bookmark');
                    const icon = button.querySelector('i');
                    icon.className = 'fas fa-bookmark';
                    button.classList.add('active');
                }
            });
        });
    }

    // Update bookmark count
    function updateBookmarkCount() {
        const count = bookmarks.length;
        const countElement = document.getElementById('bookmarkCount');
        countElement.textContent = count;
        countElement.classList.toggle('hidden', count === 0);
    }

    // Update bookmarks list
    function updateBookmarksList() {
        const bookmarksList = document.getElementById('bookmarksList');
        
        if (bookmarks.length === 0) {
            bookmarksList.innerHTML = '<p class="empty-state">Belum ada bookmark</p>';
            return;
        }
        
        const bookmarkItems = bookmarks.map(resourceName => {
            // Find resource data
            const cards = document.querySelectorAll('.resource-card');
            let resourceData = null;
            
            cards.forEach(card => {
                const cardName = card.querySelector('.resource-title').textContent;
                if (cardName === resourceName) {
                    resourceData = {
                        name: cardName,
                        type: card.dataset.type,
                        link: card.querySelector('.btn-primary').href
                    };
                }
            });
            
            if (!resourceData) return '';
            
            return `
                <div class="bookmark-item">
                    <div class="bookmark-title">${resourceData.name}</div>
                    <div class="bookmark-type">${resourceData.type}</div>
                    <div class="bookmark-actions">
                        <a href="${resourceData.link}" class="btn btn-primary btn-sm" target="_blank">
                            <i class="fas fa-external-link-alt"></i>
                            Buka
                        </a>
                        <button class="btn btn-outline btn-sm" onclick="removeBookmark('${resourceData.name}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            `;
        }).join('');
        
        bookmarksList.innerHTML = bookmarkItems;
    }

    // Remove bookmark
    function removeBookmark(resourceName) {
        bookmarks = bookmarks.filter(name => name !== resourceName);
        localStorage.setItem('resourceBookmarks', JSON.stringify(bookmarks));
        
        // Update UI
        const cards = document.querySelectorAll('.resource-card');
        cards.forEach(card => {
            const cardName = card.querySelector('.resource-title').textContent;
            if (cardName === resourceName) {
                const button = card.querySelector('.btn-bookmark');
                const icon = button.querySelector('i');
                icon.className = 'far fa-bookmark';
                button.classList.remove('active');
            }
        });
        
        updateBookmarkCount();
        updateBookmarksList();
    }

    // Clear all bookmarks
    function clearAllBookmarks() {
        if (confirm('Apakah Anda yakin ingin menghapus semua bookmark?')) {
            bookmarks = [];
            localStorage.setItem('resourceBookmarks', JSON.stringify(bookmarks));
            
            // Update UI
            document.querySelectorAll('.btn-bookmark').forEach(button => {
                const icon = button.querySelector('i');
                icon.className = 'far fa-bookmark';
                button.classList.remove('active');
            });
            
            updateBookmarkCount();
            updateBookmarksList();
        }
    }

    // Toggle bookmarks sidebar
    function toggleBookmarksSidebar() {
        const sidebar = document.getElementById('bookmarksSidebar');
        const icon = document.querySelector('.sidebar-toggle i');
        
        sidebar.classList.toggle('open');
        if (sidebar.classList.contains('open')) {
            icon.className = 'fas fa-chevron-right';
        } else {
            icon.className = 'fas fa-chevron-left';
        }
    }

    // Share resource
    function shareResource(name, link) {
        if (navigator.share) {
            navigator.share({
                title: `Sumber Daya: ${name}`,
                text: `Lihat sumber daya ini untuk Wilkerstat SE2026: ${name}`,
                url: link
            });
        } else {
            // Fallback: copy to clipboard
            const shareText = `${name} - ${link}`;
            navigator.clipboard.writeText(shareText).then(() => {
                alert('Link telah disalin ke clipboard!');
            });
        }
    }

    // Track click
    function trackClick(resourceName) {
        if (!clickStats[resourceName]) {
            clickStats[resourceName] = 0;
        }
        clickStats[resourceName]++;
        localStorage.setItem('resourceClicks', JSON.stringify(clickStats));
    }

    // Show resource details
    function showResourceDetails(name, resource) {
        document.getElementById('resourceModalTitle').textContent = `Detail: ${name}`;
        
        const modalBody = document.getElementById('resourceModalBody');
        modalBody.innerHTML = `
            <div class="resource-detail">
                <div class="detail-header">
                    <div class="detail-icon">
                        <i class="${resource.icon}"></i>
                    </div>
                    <div class="detail-info">
                        <h3>${resource.name}</h3>
                        <span class="detail-type type-${resource.type.toLowerCase()}">${resource.type}</span>
                    </div>
                </div>
                
                <div class="detail-description">
                    <h4>Deskripsi</h4>
                    <p>${resource.description}</p>
                </div>
                
                ${resource.features ? `
                <div class="detail-features">
                    <h4>Fitur Utama</h4>
                    <ul>
                        ${resource.features.map(feature => `<li>${feature}</li>`).join('')}
                    </ul>
                </div>
                ` : ''}
                
                <div class="detail-meta">
                    <h4>Informasi Tambahan</h4>
                    <div class="meta-grid">
                        ${resource.platform ? `<div class="meta-item"><strong>Platform:</strong> ${resource.platform}</div>` : ''}
                        ${resource.price ? `<div class="meta-item"><strong>Harga:</strong> ${resource.price}</div>` : ''}
                        ${resource.language ? `<div class="meta-item"><strong>Bahasa:</strong> ${resource.language}</div>` : ''}
                        <div class="meta-item"><strong>Klik:</strong> ${clickStats[resource.name] || 0} kali</div>
                    </div>
                </div>
            </div>
            
            <style>
                .resource-detail {
                    padding: 1rem 0;
                }
                .detail-header {
                    display: flex;
                    align-items: center;
                    gap: 1rem;
                    margin-bottom: 1.5rem;
                }
                .detail-icon {
                    width: 60px;
                    height: 60px;
                    border-radius: 0.5rem;
                    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                    font-size: 1.5rem;
                }
                .detail-info h3 {
                    margin: 0 0 0.5rem 0;
                    color: var(--text-primary);
                }
                .detail-type {
                    padding: 0.25rem 0.75rem;
                    border-radius: 0.25rem;
                    font-size: 0.875rem;
                    font-weight: 500;
                    color: white;
                }
                .detail-description,
                .detail-features,
                .detail-meta {
                    margin-bottom: 1.5rem;
                }
                .detail-description h4,
                .detail-features h4,
                .detail-meta h4 {
                    color: var(--text-primary);
                    margin-bottom: 0.75rem;
                    font-size: 1rem;
                }
                .detail-description p {
                    color: var(--text-secondary);
                    line-height: 1.6;
                }
                .detail-features ul {
                    list-style: none;
                    padding: 0;
                    margin: 0;
                }
                .detail-features li {
                    padding: 0.5rem 0;
                    border-bottom: 1px solid var(--border-color);
                    color: var(--text-secondary);
                }
                .detail-features li:last-child {
                    border-bottom: none;
                }
                .meta-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 0.75rem;
                }
                .meta-item {
                    padding: 0.75rem;
                    background: var(--bg-light);
                    border-radius: 0.375rem;
                    font-size: 0.875rem;
                    color: var(--text-secondary);
                }
                .meta-item strong {
                    color: var(--text-primary);
                }
            </style>
        `;
        
        const modalLink = document.getElementById('resourceModalLink');
        modalLink.onclick = () => {
            window.open(resource.link, '_blank');
            trackClick(resource.name);
        };
        
        document.getElementById('resourceModal').style.display = 'block';
    }

    // Close resource modal
    function closeResourceModal() {
        document.getElementById('resourceModal').style.display = 'none';
    }

    // Export bookmarks
    function exportBookmarks() {
        if (bookmarks.length === 0) {
            alert('Tidak ada bookmark untuk diekspor.');
            return;
        }
        
        const bookmarkData = bookmarks.map(name => {
            const cards = document.querySelectorAll('.resource-card');
            let resourceData = null;
            
            cards.forEach(card => {
                const cardName = card.querySelector('.resource-title').textContent;
                if (cardName === name) {
                    resourceData = {
                        name: cardName,
                        type: card.dataset.type,
                        link: card.querySelector('.btn-primary').href
                    };
                }
            });
            
            return resourceData;
        }).filter(item => item !== null);
        
        const dataStr = JSON.stringify(bookmarkData, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = 'wilkerstat-bookmarks.json';
        link.click();
        
        URL.revokeObjectURL(url);
    }

    // Modal close on outside click
    document.getElementById('resourceModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeResourceModal();
        }
    });

    // Close sidebar on outside click
    document.addEventListener('click', function(e) {
        const sidebar = document.getElementById('bookmarksSidebar');
        const fab = document.querySelector('.quick-access-fab');
        
        if (sidebar.classList.contains('open') && 
            !sidebar.contains(e.target) && 
            !fab.contains(e.target)) {
            toggleBookmarksSidebar();
        }
    });
</script>
@endpush