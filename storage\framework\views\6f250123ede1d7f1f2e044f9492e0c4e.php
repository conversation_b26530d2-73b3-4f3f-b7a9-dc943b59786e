<?php $__env->startSection('title', '<PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>golahan Wilkerstat SE2026'); ?>

<?php $__env->startSection('head'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/training-modern.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Modern Page Header -->
    <div class="modern-page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="modern-page-title">Materi Pelatihan Pengolahan Wilkerstat SE2026</h1>
                    <p class="modern-page-subtitle">Hub Pembelajaran Terpadu</p>
                    <p class="modern-page-description">Akses semua materi pelatihan, modul pem<PERSON>, dan panduan lengkap untuk pengolahan data Wilkerstat SE2026 dalam satu tempat yang terorganisir dan mudah digunakan.</p>
                </div>
                <div class="col-lg-4">
                    <div class="modern-material-info mt-4">
                        <div class="modern-info-badges">
                            <div class="modern-info-badge">
                                <i class="fas fa-book-open"></i>
                                <span>5 Modul</span>
                            </div>
                            <div class="modern-info-badge">
                                <i class="fas fa-file-pdf"></i>
                                <span><?php echo e($availableFilesCount); ?> PDF</span>
                            </div>
                        </div>
                        <a href="<?php echo e($googleDriveLink); ?>" class="modern-download-btn" target="_blank">
                            <i class="fab fa-google-drive"></i>
                            Google Drive Lengkap
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Breadcrumb -->
    <div class="modern-breadcrumb-section">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="modern-breadcrumb">
                    <li class="modern-breadcrumb-item">
                        <a href="<?php echo e(route('home')); ?>" class="modern-breadcrumb-link">Beranda</a>
                    </li>
                    <li class="modern-breadcrumb-item">
                        <span class="modern-breadcrumb-current">Materi Pelatihan</span>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Modern Content Layout -->
    <div class="modern-content-layout">
        <div class="container">
            <!-- Training Modules Section -->
            <div class="modern-section mb-8">
                <div class="modern-section-header text-center mb-6">
                    <h2 class="modern-section-title">
                        <i class="fas fa-graduation-cap"></i>
                        Modul Pelatihan
                    </h2>
                    <p class="modern-section-subtitle">
                        Pilih modul pelatihan sesuai dengan kebutuhan pembelajaran Anda
                    </p>
                </div>
                
                <div class="row g-4">
                    <!-- Pendahuluan Module -->
                    <div class="col-lg-6 col-xl-4">
                        <div class="modern-training-card">
                            <div class="modern-card-header">
                                <div class="modern-card-icon pendahuluan">
                                    <i class="fas fa-play-circle"></i>
                                </div>
                                <div class="modern-card-badge">
                                    <span>Modul 1</span>
                                </div>
                            </div>
                            <div class="modern-card-content">
                                <h3 class="modern-card-title">Pendahuluan</h3>
                                <p class="modern-card-description">
                                    Latar belakang, maksud dan tujuan, landasan hukum, serta instrumen dan perangkat SE2026.
                                </p>
                                <div class="modern-card-meta">
                                    <div class="modern-meta-item">
                                        <i class="fas fa-clock"></i>
                                        <span>45 menit</span>
                                    </div>
                                    <div class="modern-meta-item">
                                        <i class="fas fa-signal"></i>
                                        <span>Pemula</span>
                                    </div>
                                </div>
                            </div>
                            <div class="modern-card-footer">
                                <a href="<?php echo e(route('pendahuluan')); ?>" class="modern-btn modern-btn-primary">
                                    <i class="fas fa-book-open"></i>
                                    Pelajari Modul
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Organisasi Pengolahan Module -->
                    <div class="col-lg-6 col-xl-4">
                        <div class="modern-training-card">
                            <div class="modern-card-header">
                                <div class="modern-card-icon organisasi">
                                    <i class="fas fa-sitemap"></i>
                                </div>
                                <div class="modern-card-badge">
                                    <span>Modul 2</span>
                                </div>
                            </div>
                            <div class="modern-card-content">
                                <h3 class="modern-card-title">Organisasi Pengolahan</h3>
                                <p class="modern-card-description">
                                    Struktur organisasi dan pembagian tugas dalam pengolahan data Wilkerstat.
                                </p>
                                <div class="modern-card-meta">
                                    <div class="modern-meta-item">
                                        <i class="fas fa-clock"></i>
                                        <span>45 menit</span>
                                    </div>
                                    <div class="modern-meta-item">
                                        <i class="fas fa-signal"></i>
                                        <span>Menengah</span>
                                    </div>
                                </div>
                            </div>
                            <div class="modern-card-footer">
                                <a href="<?php echo e(route('organisasi-pengolahan')); ?>" class="modern-btn modern-btn-primary">
                                    <i class="fas fa-book-open"></i>
                                    Pelajari Modul
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Mekanisme Pengolahan Module -->
                    <div class="col-lg-6 col-xl-4">
                        <div class="modern-training-card">
                            <div class="modern-card-header">
                                <div class="modern-card-icon mekanisme">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <div class="modern-card-badge">
                                    <span>Modul 3</span>
                                </div>
                            </div>
                            <div class="modern-card-content">
                                <h3 class="modern-card-title">Mekanisme Pengolahan</h3>
                                <p class="modern-card-description">
                                    Prosedur dan mekanisme pengolahan data secara sistematis dan terstruktur.
                                </p>
                                <div class="modern-card-meta">
                                    <div class="modern-meta-item">
                                        <i class="fas fa-clock"></i>
                                        <span>60 menit</span>
                                    </div>
                                    <div class="modern-meta-item">
                                        <i class="fas fa-signal"></i>
                                        <span>Menengah</span>
                                    </div>
                                </div>
                            </div>
                            <div class="modern-card-footer">
                                <a href="<?php echo e(route('materi-mekanisme-pen')); ?>" class="modern-btn modern-btn-primary">
                                    <i class="fas fa-book-open"></i>
                                    Pelajari Modul
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- SIPW SE2026 Module -->
                    <div class="col-lg-6 col-xl-4">
                        <div class="modern-training-card">
                            <div class="modern-card-header">
                                <div class="modern-card-icon sipw">
                                    <i class="fas fa-desktop"></i>
                                </div>
                                <div class="modern-card-badge">
                                    <span>Modul 4</span>
                                </div>
                            </div>
                            <div class="modern-card-content">
                                <h3 class="modern-card-title">SIPW SE2026</h3>
                                <p class="modern-card-description">
                                    Panduan lengkap penggunaan aplikasi SiPW untuk pemutakhiran data Wilkerstat.
                                </p>
                                <div class="modern-card-meta">
                                    <div class="modern-meta-item">
                                        <i class="fas fa-clock"></i>
                                        <span>90 menit</span>
                                    </div>
                                    <div class="modern-meta-item">
                                        <i class="fas fa-signal"></i>
                                        <span>Lanjutan</span>
                                    </div>
                                </div>
                            </div>
                            <div class="modern-card-footer">
                                <a href="<?php echo e(route('sipw-se2026')); ?>" class="modern-btn modern-btn-primary">
                                    <i class="fas fa-book-open"></i>
                                    Pelajari Modul
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Pengolahan Peta Module -->
                    <div class="col-lg-6 col-xl-4">
                        <div class="modern-training-card">
                            <div class="modern-card-header">
                                <div class="modern-card-icon peta">
                                    <i class="fas fa-map"></i>
                                </div>
                                <div class="modern-card-badge">
                                    <span>Modul 5</span>
                                </div>
                            </div>
                            <div class="modern-card-content">
                                <h3 class="modern-card-title">Pengolahan Peta</h3>
                                <p class="modern-card-description">
                                    Teknik dan metode pengolahan peta digital untuk keperluan Wilkerstat.
                                </p>
                                <div class="modern-card-meta">
                                    <div class="modern-meta-item">
                                        <i class="fas fa-clock"></i>
                                        <span>75 menit</span>
                                    </div>
                                    <div class="modern-meta-item">
                                        <i class="fas fa-signal"></i>
                                        <span>Lanjutan</span>
                                    </div>
                                </div>
                            </div>
                            <div class="modern-card-footer">
                                <a href="<?php echo e(route('pengolahan-peta')); ?>" class="modern-btn modern-btn-primary">
                                    <i class="fas fa-book-open"></i>
                                    Pelajari Modul
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Buku Pedoman Section -->
                    <div class="col-lg-6 col-xl-4">
                        <div class="modern-training-card modern-manual-card">
                            <div class="modern-card-header">
                                <div class="modern-card-icon manual">
                                    <i class="fas fa-book"></i>
                                </div>
                                <div class="modern-card-badge manual">
                                    <span>Manual</span>
                                </div>
                            </div>
                            <div class="modern-card-content">
                                <h3 class="modern-card-title">Buku Pedoman Lengkap</h3>
                                <p class="modern-card-description">
                                    Panduan komprehensif berisi semua materi pelatihan dalam satu dokumen lengkap.
                                </p>
                                <div class="modern-card-meta">
                                    <div class="modern-meta-item">
                                        <i class="fas fa-file-pdf"></i>
                                        <span>PDF Lengkap</span>
                                    </div>
                                    <div class="modern-meta-item">
                                        <i class="fas fa-download"></i>
                                        <span>Siap Unduh</span>
                                    </div>
                                </div>
                            </div>
                            <div class="modern-card-footer">
                                <a href="<?php echo e(asset('bahanajar/[FINAL] Buku 3 Pedoman Pengolahan Pemutakhiran Wilkerstat SE2026.pdf')); ?>" class="modern-btn modern-btn-secondary" target="_blank">
                                    <i class="fas fa-download"></i>
                                    Download PDF
                                </a>
                                <a href="<?php echo e($googleDriveLink); ?>" class="modern-btn modern-btn-outline" target="_blank">
                                    <i class="fab fa-google-drive"></i>
                                    Google Drive
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="<?php echo e(asset('js/training-modern.js')); ?>"></script>
<script>
// Modern Materials Hub JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize modern animations
    initializeModernAnimations();
    
    // Add hover effects to training cards
    const trainingCards = document.querySelectorAll('.modern-training-card');
    trainingCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
            this.style.boxShadow = '0 20px 40px rgba(0,0,0,0.1)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 10px 30px rgba(0,0,0,0.08)';
        });
    });
    
    // Add special styling for manual card
    const manualCard = document.querySelector('.modern-manual-card');
    if (manualCard) {
        manualCard.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
            this.style.boxShadow = '0 25px 50px rgba(52, 152, 219, 0.15)';
        });
        
        manualCard.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.boxShadow = '0 10px 30px rgba(0,0,0,0.08)';
        });
    }
    
    // Track module access
    const moduleLinks = document.querySelectorAll('.modern-training-card .modern-btn-primary');
    moduleLinks.forEach(link => {
        link.addEventListener('click', function() {
            const cardTitle = this.closest('.modern-training-card').querySelector('.modern-card-title').textContent;
            console.log('Accessing module:', cardTitle);
            // Add analytics tracking here if needed
        });
    });
    
    // Track manual downloads
    const downloadLinks = document.querySelectorAll('.modern-btn-secondary, .modern-btn-outline');
    downloadLinks.forEach(link => {
        link.addEventListener('click', function() {
            const action = this.textContent.includes('Download') ? 'download' : 'drive_access';
            console.log('Manual action:', action);
            // Add analytics tracking here if needed
        });
    });
});

function initializeModernAnimations() {
    // Staggered animation for cards
    const cards = document.querySelectorAll('.modern-training-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s cubic-bezier(0.4, 0, 0.2, 1)';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
    
    // Animate section header
    const sectionHeader = document.querySelector('.modern-section-header');
    if (sectionHeader) {
        sectionHeader.style.opacity = '0';
        sectionHeader.style.transform = 'translateY(-20px)';
        
        setTimeout(() => {
            sectionHeader.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)';
            sectionHeader.style.opacity = '1';
            sectionHeader.style.transform = 'translateY(0)';
        }, 200);
    }
 </script>
 <?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\pengolahanwilker\resources\views/training/materials.blade.php ENDPATH**/ ?>