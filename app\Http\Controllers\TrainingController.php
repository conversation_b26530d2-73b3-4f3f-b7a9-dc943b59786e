<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class TrainingController extends Controller
{
    /**
     * Display the homepage
     */
    public function index()
    {
        return view('training.index');
    }

    /**
     * Display training schedule
     */
    public function schedule()
    {
        // <PERSON><PERSON><PERSON> Pelatihan Petugas Pengolahan Wilkerstat SE2026
        // Pemutakhiran Kerangka Geospasial dan Muatan Wilkerstat
        $schedules = [
            // Jumat, 25 Juli 2025 - Pembelajaran Mandiri
            [
                'date' => '2025-07-25',
                'day' => 'Jumat',
                'type' => 'Pembelajaran Mandiri',
                'time' => '08:00–16:00',
                'session' => 'MOOC',
                'material' => 'Calon Petugas mempelajari secara mandiri seluruh bahan pelatihan (buku pedoman, bahan ajar, video pembelajaran). <PERSON>on Petugas mengumpulkan tugas MOOC.',
                'class_type' => 'Mandiri',
                'jp' => '3',
                'instructor' => '-'
            ],
            
            // Senin, 28 Juli 2025 - <PERSON><PERSON><PERSON><PERSON><PERSON> (8 JP)
            [
                'date' => '2025-07-28',
                'day' => 'Senin',
                'type' => 'Pembelajaran Daring (8 JP)',
                'time' => '08:00–08:15',
                'session' => '-',
                'material' => 'Pre-test',
                'class_type' => 'Kelas',
                'jp' => '-',
                'instructor' => 'Admin'
            ],
            [
                'date' => '2025-07-28',
                'day' => 'Senin',
                'type' => 'Pembelajaran Daring (8 JP)',
                'time' => '08:15–09:00',
                'session' => 'Sesi 1',
                'material' => 'Organisasi Pengolahan: Pendahuluan, latar belakang, maksud dan tujuan, landasan hukum. Jenis instrumen dan perangkat, jadwal kegiatan. Admin BPS Kabupaten/Kota, Petugas Pengolahan',
                'class_type' => 'Kelas',
                'jp' => '1',
                'instructor' => 'Inda'
            ],
            [
                'date' => '2025-07-28',
                'day' => 'Senin',
                'type' => 'Pembelajaran Daring (8 JP)',
                'time' => '09:00–09:45',
                'session' => 'Sesi 2',
                'material' => 'Mekanisme Pengolahan Muatan SLS: Mekanisme pengolahan secara umum. Master Wilkerstat dan Muatan SLS',
                'class_type' => 'Kelas',
                'jp' => '1',
                'instructor' => 'Inda'
            ],
            [
                'date' => '2025-07-28',
                'day' => 'Senin',
                'type' => 'Pembelajaran Daring (8 JP)',
                'time' => '09:45–10:00',
                'session' => '-',
                'material' => 'Istirahat',
                'class_type' => '-',
                'jp' => '-',
                'instructor' => '-'
            ],
            [
                'date' => '2025-07-28',
                'day' => 'Senin',
                'type' => 'Pembelajaran Daring (8 JP)',
                'time' => '10:00–10:45',
                'session' => 'Sesi 3',
                'material' => 'Mekanisme Pengolahan Peta',
                'class_type' => 'Kelas',
                'jp' => '1',
                'instructor' => 'Inda'
            ],
            [
                'date' => '2025-07-28',
                'day' => 'Senin',
                'type' => 'Pembelajaran Daring (8 JP)',
                'time' => '10:45–12:15',
                'session' => 'Sesi 4',
                'material' => 'Aplikasi SiPW: Pengenalan Aplikasi, Persiapan dan Sinkronisasi dengan FRS, Assign Petugas, Cetak Dokumen. Tata Cara Entri Dokumen SE2026-WILKERSTAT.RS',
                'class_type' => 'Kelas',
                'jp' => '2',
                'instructor' => 'Inda'
            ],
            [
                'date' => '2025-07-28',
                'day' => 'Senin',
                'type' => 'Pembelajaran Daring (8 JP)',
                'time' => '12:15–13:00',
                'session' => '-',
                'material' => 'ISHOMA',
                'class_type' => '-',
                'jp' => '-',
                'instructor' => '-'
            ],
            [
                'date' => '2025-07-28',
                'day' => 'Senin',
                'type' => 'Pembelajaran Daring (8 JP)',
                'time' => '13:00–13:45',
                'session' => 'Sesi 5',
                'material' => 'Pengolahan Peta: Alat dan bahan, Penyiapan pengolahan peta, Penyiapan bahan',
                'class_type' => 'Kelas',
                'jp' => '1',
                'instructor' => 'Inda'
            ],
            [
                'date' => '2025-07-28',
                'day' => 'Senin',
                'type' => 'Pembelajaran Daring (8 JP)',
                'time' => '13:45–14:30',
                'session' => 'Sesi 6',
                'material' => 'Pengolahan Peta (Lanjutan): Georeferensi peta',
                'class_type' => 'Kelas',
                'jp' => '1',
                'instructor' => 'Inda'
            ],
            [
                'date' => '2025-07-28',
                'day' => 'Senin',
                'type' => 'Pembelajaran Daring (8 JP)',
                'time' => '14:30–15:15',
                'session' => 'Sesi 7',
                'material' => 'Pengolahan Peta (Lanjutan): Editing peta digital (Menggabungkan Poligon dan Memotong Poligon), Cleaning dan validasi',
                'class_type' => 'Kelas',
                'jp' => '1',
                'instructor' => 'Inda'
            ],
            [
                'date' => '2025-07-28',
                'day' => 'Senin',
                'type' => 'Pembelajaran Daring (8 JP)',
                'time' => '15:15–16:00',
                'session' => '-',
                'material' => 'Asynchronous 1: Tugas terkait pengolahan muatan',
                'class_type' => 'Kelas',
                'jp' => '-',
                'instructor' => 'Admin'
            ],
            
            // Selasa, 29 Juli 2025 - Pembelajaran Tatap Muka (Luring, 8 JP)
            [
                'date' => '2025-07-29',
                'day' => 'Selasa',
                'type' => 'Pembelajaran Tatap Muka (Luring, 8 JP)',
                'time' => '07:30–07:45',
                'session' => '-',
                'material' => 'Registrasi',
                'class_type' => '-',
                'jp' => '-',
                'instructor' => 'Panitia'
            ],
            [
                'date' => '2025-07-29',
                'day' => 'Selasa',
                'type' => 'Pembelajaran Tatap Muka (Luring, 8 JP)',
                'time' => '07:45–08:00',
                'session' => '-',
                'material' => 'Pembukaan',
                'class_type' => '-',
                'jp' => '-',
                'instructor' => 'Kepala BPS Kabupaten/Kota'
            ],
            [
                'date' => '2025-07-29',
                'day' => 'Selasa',
                'type' => 'Pembelajaran Tatap Muka (Luring, 8 JP)',
                'time' => '08:00–08:45',
                'session' => 'Sesi 8',
                'material' => 'Pengolahan Peta (Lanjutan): Dissolving',
                'class_type' => 'Kelas',
                'jp' => '1',
                'instructor' => 'Inda'
            ],
            [
                'date' => '2025-07-29',
                'day' => 'Selasa',
                'type' => 'Pembelajaran Tatap Muka (Luring, 8 JP)',
                'time' => '08:45–10:15',
                'session' => 'Sesi 9',
                'material' => 'Praktik Penggunaan Aplikasi SiPW',
                'class_type' => 'Kelas',
                'jp' => '2',
                'instructor' => 'Inda'
            ],
            [
                'date' => '2025-07-29',
                'day' => 'Selasa',
                'type' => 'Pembelajaran Tatap Muka (Luring, 8 JP)',
                'time' => '10:15–10:30',
                'session' => '-',
                'material' => 'Istirahat',
                'class_type' => '-',
                'jp' => '-',
                'instructor' => '-'
            ],
            [
                'date' => '2025-07-29',
                'day' => 'Selasa',
                'type' => 'Pembelajaran Tatap Muka (Luring, 8 JP)',
                'time' => '10:30–11:15',
                'session' => 'Sesi 10',
                'material' => 'Praktik Penggunaan Aplikasi SiPW (Lanjutan)',
                'class_type' => 'Kelas',
                'jp' => '1',
                'instructor' => 'Inda'
            ],
            [
                'date' => '2025-07-29',
                'day' => 'Selasa',
                'type' => 'Pembelajaran Tatap Muka (Luring, 8 JP)',
                'time' => '11:15–12:00',
                'session' => 'Sesi 11',
                'material' => 'Praktik Pengolahan Peta: Praktik editing peta digital',
                'class_type' => 'Kelas',
                'jp' => '1',
                'instructor' => 'Inda'
            ],
            [
                'date' => '2025-07-29',
                'day' => 'Selasa',
                'type' => 'Pembelajaran Tatap Muka (Luring, 8 JP)',
                'time' => '12:00–13:00',
                'session' => '-',
                'material' => 'ISHOMA',
                'class_type' => '-',
                'jp' => '-',
                'instructor' => '-'
            ],
            [
                'date' => '2025-07-29',
                'day' => 'Selasa',
                'type' => 'Pembelajaran Tatap Muka (Luring, 8 JP)',
                'time' => '13:00–15:15',
                'session' => 'Sesi 12',
                'material' => 'Praktik Pengolahan Peta (Lanjutan): Praktik editing peta digital',
                'class_type' => 'Kelas',
                'jp' => '3',
                'instructor' => 'Inda'
            ],
            [
                'date' => '2025-07-29',
                'day' => 'Selasa',
                'type' => 'Pembelajaran Tatap Muka (Luring, 8 JP)',
                'time' => '15:15–15:30',
                'session' => '-',
                'material' => 'Istirahat',
                'class_type' => '-',
                'jp' => '-',
                'instructor' => '-'
            ],
            [
                'date' => '2025-07-29',
                'day' => 'Selasa',
                'type' => 'Pembelajaran Tatap Muka (Luring, 8 JP)',
                'time' => '15:30–16:15',
                'session' => '-',
                'material' => 'Asynchronous: Tugas terkait pengolahan peta',
                'class_type' => 'Kelas',
                'jp' => '-',
                'instructor' => 'Admin'
            ],
            [
                'date' => '2025-07-29',
                'day' => 'Selasa',
                'type' => 'Pembelajaran Tatap Muka (Luring, 8 JP)',
                'time' => '16:15–16:30',
                'session' => '-',
                'material' => 'Post Test',
                'class_type' => '-',
                'jp' => '-',
                'instructor' => 'Admin'
            ],
            [
                'date' => '2025-07-29',
                'day' => 'Selasa',
                'type' => 'Pembelajaran Tatap Muka (Luring, 8 JP)',
                'time' => '16:30–17:00',
                'session' => '-',
                'material' => 'Penegasan dan Penutupan Pelatihan',
                'class_type' => 'Pleno',
                'jp' => '-',
                'instructor' => 'Kepala BPS Kabupaten/Kota'
            ]
        ];

        return view('training.schedule', compact('schedules'));
    }

    /**
     * Display training materials
     */
    public function materials()
    {
        // Get actual materials from bahanajar folder
        $bahanAjarPath = public_path('bahanajar');
        $availableFiles = [];
        
        if (is_dir($bahanAjarPath)) {
            $files = scandir($bahanAjarPath);
            foreach ($files as $file) {
                if (pathinfo($file, PATHINFO_EXTENSION) === 'pdf') {
                    $availableFiles[] = [
                        'filename' => $file,
                        'title' => $this->formatFileName($file),
                        'path' => asset('bahanajar/' . $file),
                        'size' => $this->formatFileSize(filesize($bahanAjarPath . '/' . $file))
                    ];
                }
            }
        }
        
        // Organize materials by category based on content
        $materials = [
            'Materi Dasar Pengolahan' => [
                [
                    'title' => '01 Pendahuluan Wilkerstat SE2026',
                    'type' => 'PDF',
                    'link' => asset('bahanajar/01 Pendahuluan.pdf'),
                    'description' => 'Latar belakang pemutakhiran SLS, maksud dan tujuan kegiatan, landasan hukum, jenis instrumen dan perangkat, serta jadwal kegiatan SE2026.',
                    'category' => 'fundamental',
                    'duration' => '45 menit',
                    'difficulty' => 'Pemula',
                    'detailed_link' => route('pendahuluan'),
                    'detailed_content' => [
                        'latar_belakang' => [
                            'title' => 'Latar Belakang',
                            'content' => [
                                'Satuan Lingkungan Setempat (SLS) adalah wilayah di bawah desa/kelurahan yang memiliki ketua dan pengurus yang telah operasional dan diakui oleh pemerintah desa/kelurahan.',
                                'Tantangan utama SLS adalah sifatnya yang sangat dinamis, sehingga memerlukan pemutakhiran sebelum pelaksanaan Sensus Ekonomi 2026 (SE2026).',
                                'Kegiatan Pemutakhiran Kerangka Geospasial dan Muatan Wilkerstat SE2026 dilakukan pada tahun 2025 dengan dua tahapan:'
                            ],
                            'tahapan' => [
                                'lapangan' => [
                                    'title' => 'Kegiatan Lapangan',
                                    'items' => [
                                        'Pemutakhiran peta dan SLS',
                                        'Geotagging batas SLS dan kawasan ekonomi',
                                        'Pembentukan sub-SLS'
                                    ]
                                ],
                                'pengolahan' => [
                                    'title' => 'Kegiatan Pengolahan',
                                    'items' => [
                                        'Pengolahan master dan muatan SLS',
                                        'Pengolahan peta wilkerstat hasil kegiatan lapangan'
                                    ]
                                ]
                            ],
                            'kesimpulan' => 'Latar belakang menjelaskan pentingnya pemutakhiran SLS karena sifatnya yang dinamis, dengan kegiatan SE2026 yang terdiri dari tahapan lapangan (pemutakhiran peta, geotagging, pembentukan sub-SLS) dan pengolahan (master, muatan, dan peta wilkerstat) untuk mendukung Sensus Ekonomi 2026.'
                        ],
                        'maksud_tujuan' => [
                            'title' => 'Maksud dan Tujuan',
                            'content' => [
                                'Mendapatkan muatan wilkerstat yang seragam dan mutakhir',
                                'Mendapatkan kerangka geospasial yang mutakhir',
                                'Mendapatkan informasi mengenai wilayah konsentrasi ekonomi'
                            ],
                            'kesimpulan' => 'Tujuan kegiatan adalah menghasilkan data wilkerstat dan kerangka geospasial yang terbaru dan konsisten, serta informasi tentang wilayah konsentrasi ekonomi untuk mendukung pelaksanaan SE2026.'
                        ],
                        'landasan_hukum' => [
                            'title' => 'Landasan Hukum',
                            'content' => 'Dokumen tidak memberikan rincian spesifik tentang landasan hukum, tetapi menyebutkan bahwa kegiatan ini memiliki dasar hukum yang mendukung pelaksanaan pemutakhiran wilkerstat.',
                            'kesimpulan' => 'Landasan hukum menjadi dasar formal untuk pelaksanaan kegiatan pemutakhiran, meskipun detail spesifik tidak diuraikan dalam dokumen.'
                        ],
                        'instrumen_perangkat' => [
                            'title' => 'Jenis Instrumen dan Perangkat',
                            'pengolahan_master' => [
                                'title' => 'Instrumen Pengolahan Master dan Muatan SLS',
                                'items' => [
                                    'Daftar Perubahan SLS (Daftar PSLS): Berisi informasi SLS yang mengalami perubahan dari hasil pemutakhiran di lapangan',
                                    'SE2026-WILKERSTAT.RS: Daftar Rekap SLS hasil pemutakhiran, memuat informasi muatan tiap SLS/Non-SLS per desa/kelurahan',
                                    'Aplikasi Frame Register: Digunakan untuk pengelolaan data SLS'
                                ]
                            ],
                            'pengolahan_peta' => [
                                'title' => 'Instrumen Pengolahan Peta',
                                'items' => [
                                    'Peta digital SLS kondisi terbaru: Dapat diunduh dari Geospatial System (GS) di https://dataspasial.bps.go.id/gs (periode 2024.1) atau versi terbaru dari BPS Kabupaten/Kota',
                                    'Geotagging batas SLS: Melalui https://wilkerstat.bps.go.id untuk informasi lokasi dan perubahan batas SLS',
                                    'Peta WS hasil pemutakhiran di lapangan: Memberikan informasi perbaikan batas SLS',
                                    'Lembar Kerja Peta (LK-Peta): Hasil penggambaran sketsa peta untuk di-scan',
                                    'Master SLS Snapshot Tahun 2025 Semester 1: Data dasar untuk pemutakhiran'
                                ]
                            ],
                            'software_aplikasi' => [
                                'title' => 'Software dan Aplikasi',
                                'items' => [
                                    'QGIS: Digunakan untuk editing peta digital',
                                    'Geospatial System (GS): Untuk pengiriman, validasi, dan penyimpanan peta digital hasil kegiatan',
                                    'Aplikasi pendukung lainnya: Alat bantu untuk pemrosesan dan pengolahan peta, tersedia untuk diunduh gratis'
                                ]
                            ],
                            'kesimpulan' => 'Instrumen dan perangkat mencakup dokumen (Daftar PSLS, SE2026-WILKERSTAT.RS, LK-Peta), aplikasi (Frame Register, GS), dan software (QGIS) untuk mendukung pengolahan master, muatan SLS, dan peta wilkerstat, dengan akses ke data terbaru melalui platform online.'
                        ],
                        'jadwal_kegiatan' => [
                            'title' => 'Jadwal Kegiatan',
                            'content' => 'Dokumen menyebutkan adanya jadwal kegiatan untuk tahun 2025, tetapi tidak memberikan rincian spesifik dalam teks yang tersedia.',
                            'kesimpulan' => 'Jadwal kegiatan disusun untuk mengatur pelaksanaan pemutakhiran wilkerstat pada tahun 2025, meskipun detailnya tidak diuraikan dalam dokumen ini.'
                        ]
                    ]
                ],
                [
                    'title' => 'Organisasi Pengolahan',
                    'type' => 'PDF',
                    'link' => asset('bahanajar/02 Organisasi Pengolahan.pdf'),
                    'description' => 'Struktur organisasi dan pembagian tugas dalam pengolahan data Wilkerstat.',
                    'category' => 'fundamental',
                    'duration' => '45 menit',
                    'difficulty' => 'Menengah',
                    'detailed_link' => route('organisasi-pengolahan')
                ]
            ],
            'Mekanisme dan Prosedur' => [
                [
                    'title' => 'Mekanisme Pengolahan',
                    'type' => 'PDF',
                    'link' => asset('bahanajar/03 Mekanisme Pengolahan.pdf'),
                    'description' => 'Prosedur dan mekanisme pengolahan data secara sistematis dan terstruktur.',
                    'category' => 'procedure',
                    'duration' => '60 menit',
                    'difficulty' => 'Menengah',
                    'detailed_link' => route('materi-mekanisme-pen')
                ]
            ],
            'Sistem dan Aplikasi' => [
                [
                    'title' => 'Sistem Pemutakhiran Wilkerstat (SiPW) SE2026',
                    'type' => 'PDF',
                    'link' => asset('bahanajar/04.2a Sistem Pemutakhiran Wilkerstat (SiPW) SE2026 v.2.pdf'),
                    'description' => 'Panduan lengkap penggunaan aplikasi SiPW untuk pemutakhiran data Wilkerstat.',
                    'category' => 'application',
                    'duration' => '90 menit',
                    'difficulty' => 'Lanjutan',
                    'detailed_link' => route('sipw-se2026')
                ]
            ],
            'Pengolahan Peta dan Geospasial' => [
                [
                    'title' => 'Pengolahan Peta',
                    'type' => 'PDF',
                    'link' => asset('bahanajar/05 Pengolahan Peta.pdf'),
                    'description' => 'Teknik dan metode pengolahan peta digital untuk keperluan Wilkerstat.',
                    'category' => 'geospatial',
                    'duration' => '75 menit',
                    'difficulty' => 'Lanjutan'
                ]
            ]
        ];
        
        // Add Google Drive link for complete collection
        $googleDriveLink = 'https://drive.google.com/drive/folders/1Nga_528UmmatTh4Z70z8Z6P?usp=sharing';
        
        // Calculate summary statistics
        $totalMaterials = collect($materials)->flatten(1)->count();
        $totalCategories = count($materials);
        $availableFilesCount = count($availableFiles);
        
        return view('training.materials', compact('materials', 'googleDriveLink', 'totalMaterials', 'totalCategories', 'availableFiles', 'availableFilesCount'));
    }
    
    /**
     * Format filename for display
     */
    private function formatFileName($filename)
    {
        // Remove extension and format the name
        $name = pathinfo($filename, PATHINFO_FILENAME);
        // Remove numbers and clean up
        $name = preg_replace('/^\d+\.?\d*\s*/', '', $name);
        return ucwords(str_replace(['_', '-'], ' ', $name));
    }
    
    /**
     * Format file size for display
     */
    private function formatFileSize($bytes)
    {
        if ($bytes >= 1048576) {
            return number_format($bytes / 1048576, 2) . ' MB';
        } elseif ($bytes >= 1024) {
            return number_format($bytes / 1024, 2) . ' KB';
        } else {
            return $bytes . ' bytes';
        }
    }

    /**
     * Display processing guidelines
     */
    public function guidelines()
    {
        return view('training.guidelines');
    }

    /**
     * Display additional resources
     */
    public function resources()
    {
        $resources = [
            'Software & Tools' => [
                [
                    'name' => 'SPSS Statistics',
                    'description' => 'Software analisis statistik profesional untuk pengolahan data survei dan penelitian.',
                    'type' => 'Tool',
                    'icon' => 'fas fa-chart-bar',
                    'link' => 'https://www.ibm.com/spss',
                    'platform' => 'Windows, Mac, Linux',
                    'price' => 'Berbayar',
                    'language' => 'Multi-bahasa',
                    'features' => [
                        'Analisis statistik lanjutan',
                        'Visualisasi data interaktif',
                        'Machine learning',
                        'Integrasi database',
                        'Scripting otomatis'
                    ]
                ],
                [
                    'name' => 'R Statistical Software',
                    'description' => 'Bahasa pemrograman dan lingkungan untuk komputasi statistik dan grafik.',
                    'type' => 'Tool',
                    'icon' => 'fab fa-r-project',
                    'link' => 'https://www.r-project.org/',
                    'platform' => 'Cross-platform',
                    'price' => 'Gratis',
                    'language' => 'Inggris',
                    'features' => [
                        'Open source',
                        'Ribuan package tersedia',
                        'Visualisasi canggih',
                        'Komunitas aktif',
                        'Integrasi dengan tools lain'
                    ]
                ],
                [
                    'name' => 'Python for Data Science',
                    'description' => 'Bahasa pemrograman dengan library powerful untuk analisis data dan machine learning.',
                    'type' => 'Tool',
                    'icon' => 'fab fa-python',
                    'link' => 'https://www.python.org/',
                    'platform' => 'Cross-platform',
                    'price' => 'Gratis',
                    'language' => 'Multi-bahasa',
                    'features' => [
                        'Pandas untuk manipulasi data',
                        'NumPy untuk komputasi numerik',
                        'Matplotlib untuk visualisasi',
                        'Scikit-learn untuk ML',
                        'Jupyter Notebook'
                    ]
                ]
            ],
            'Database & API' => [
                [
                    'name' => 'BPS Web API',
                    'description' => 'API resmi BPS untuk mengakses data statistik Indonesia secara real-time.',
                    'type' => 'Database',
                    'icon' => 'fas fa-database',
                    'link' => 'https://webapi.bps.go.id/',
                    'platform' => 'Web-based',
                    'price' => 'Gratis',
                    'language' => 'Indonesia, Inggris',
                    'features' => [
                        'Data real-time',
                        'Format JSON/XML',
                        'Dokumentasi lengkap',
                        'Rate limiting',
                        'Authentication'
                    ]
                ],
                [
                    'name' => 'Satu Data Indonesia',
                    'description' => 'Portal data terpadu pemerintah Indonesia dengan berbagai dataset statistik.',
                    'type' => 'Database',
                    'icon' => 'fas fa-server',
                    'link' => 'https://data.go.id/',
                    'platform' => 'Web-based',
                    'price' => 'Gratis',
                    'language' => 'Indonesia',
                    'features' => [
                        'Dataset pemerintah',
                        'Format CSV, JSON, XML',
                        'Metadata lengkap',
                        'Visualisasi built-in',
                        'API access'
                    ]
                ]
            ],
            'Dokumentasi & Panduan' => [
                [
                    'name' => 'Manual Wilkerstat SE2026',
                    'description' => 'Panduan lengkap penggunaan aplikasi Wilkerstat untuk Sensus Ekonomi 2026.',
                    'type' => 'Documentation',
                    'icon' => 'fas fa-book',
                    'link' => 'https://bps.go.id/manual/wilkerstat-se2026',
                    'platform' => 'PDF, Web',
                    'price' => 'Gratis',
                    'language' => 'Indonesia',
                    'features' => [
                        'Panduan step-by-step',
                        'Screenshot aplikasi',
                        'Troubleshooting',
                        'FAQ',
                        'Update berkala'
                    ]
                ],
                [
                    'name' => 'Metadata SE2026',
                    'description' => 'Dokumentasi metadata lengkap untuk Sensus Ekonomi 2026.',
                    'type' => 'Documentation',
                    'icon' => 'fas fa-file-alt',
                    'link' => 'https://bps.go.id/metadata/se2026',
                    'platform' => 'PDF, Web',
                    'price' => 'Gratis',
                    'language' => 'Indonesia',
                    'features' => [
                        'Definisi variabel',
                        'Klasifikasi standar',
                        'Metodologi',
                        'Kuesioner',
                        'Konsep dasar'
                    ]
                ]
            ],
            'Pembelajaran Online' => [
                [
                    'name' => 'BPS Learning Center',
                    'description' => 'Platform e-learning resmi BPS dengan berbagai kursus statistik.',
                    'type' => 'Learning',
                    'icon' => 'fas fa-graduation-cap',
                    'link' => 'https://learning.bps.go.id/',
                    'platform' => 'Web-based',
                    'price' => 'Gratis',
                    'language' => 'Indonesia',
                    'features' => [
                        'Video pembelajaran',
                        'Quiz interaktif',
                        'Sertifikat',
                        'Progress tracking',
                        'Forum diskusi'
                    ]
                ],
                [
                    'name' => 'Coursera Statistics',
                    'description' => 'Kursus statistik dan data science dari universitas terkemuka dunia.',
                    'type' => 'Learning',
                    'icon' => 'fas fa-university',
                    'link' => 'https://www.coursera.org/browse/data-science/data-analysis',
                    'platform' => 'Web, Mobile',
                    'price' => 'Freemium',
                    'language' => 'Multi-bahasa',
                    'features' => [
                        'Kursus dari universitas top',
                        'Hands-on projects',
                        'Peer review',
                        'Professional certificates',
                        'Financial aid available'
                    ]
                ]
            ]
        ];

        return view('training.resources', compact('resources'));
    }

    /**
     * Display contact and help information
     */
    public function contact()
    {
        $contact = [
            'phone' => '+62-21-3841195',
            'email' => '<EMAIL>',
            'whatsapp' => '+62-812-3456-7890',
            'address' => 'Jl. Dr. Sutomo No. 6-8, Jakarta Pusat 10710, Indonesia',
            'office_name' => 'Badan Pusat Statistik (BPS) - Kantor Pusat'
        ];

        $faqs = [
            'general' => [
                [
                    'question' => 'Apa itu Wilkerstat SE2026?',
                    'answer' => 'Wilkerstat SE2026 adalah aplikasi pengolahan data untuk Sensus Ekonomi 2026 yang dikembangkan oleh BPS. Aplikasi ini digunakan untuk memproses, menganalisis, dan menghasilkan statistik dari data sensus ekonomi.',
                    'links' => [
                        ['title' => 'Panduan Wilkerstat', 'url' => '/panduan'],
                        ['title' => 'Download Manual', 'url' => '#downloads']
                    ]
                ],
                [
                    'question' => 'Siapa yang dapat mengikuti pelatihan ini?',
                    'answer' => 'Pelatihan ini ditujukan untuk petugas BPS, mitra kerja, dan stakeholder yang terlibat dalam Sensus Ekonomi 2026. Peserta diharapkan memiliki pemahaman dasar tentang statistik dan penggunaan komputer.',
                ],
                [
                    'question' => 'Apakah pelatihan ini gratis?',
                    'answer' => 'Ya, pelatihan Wilkerstat SE2026 disediakan secara gratis oleh BPS sebagai bagian dari program capacity building untuk Sensus Ekonomi 2026.',
                ]
            ],
            'technical' => [
                [
                    'question' => 'Apa persyaratan sistem untuk menjalankan Wilkerstat?',
                    'answer' => 'Wilkerstat membutuhkan Windows 10 atau lebih baru, RAM minimal 4GB (disarankan 8GB), ruang disk 2GB, dan koneksi internet untuk sinkronisasi data.',
                ],
                [
                    'question' => 'Bagaimana cara mengunduh dan menginstal Wilkerstat?',
                    'answer' => 'Anda dapat mengunduh installer Wilkerstat dari portal resmi BPS. Ikuti panduan instalasi yang disediakan dan pastikan komputer Anda memenuhi persyaratan sistem.',
                ],
                [
                    'question' => 'Apa yang harus dilakukan jika aplikasi error?',
                    'answer' => 'Jika mengalami error, coba restart aplikasi terlebih dahulu. Jika masalah berlanjut, periksa log error dan hubungi tim support dengan menyertakan detail error yang terjadi.',
                ]
            ],
            'training' => [
                [
                    'question' => 'Berapa lama durasi pelatihan?',
                    'answer' => 'Pelatihan Wilkerstat SE2026 berlangsung selama 3 hari dengan total 24 jam pembelajaran, termasuk sesi teori, praktik, dan evaluasi.',
                ],
                [
                    'question' => 'Apakah ada sertifikat setelah menyelesaikan pelatihan?',
                    'answer' => 'Ya, peserta yang menyelesaikan seluruh materi pelatihan dan lulus evaluasi akan mendapatkan sertifikat resmi dari BPS.',
                ],
                [
                    'question' => 'Bagaimana cara mendaftar pelatihan?',
                    'answer' => 'Pendaftaran dilakukan melalui sistem online BPS atau melalui koordinator di kantor BPS daerah masing-masing. Informasi pendaftaran akan diumumkan melalui website resmi BPS.',
                ]
            ],
            'materials' => [
                [
                    'question' => 'Di mana saya bisa mengakses materi pelatihan?',
                    'answer' => 'Semua materi pelatihan tersedia di halaman Materi Pelatihan website ini. Materi dapat diunduh dalam format PDF dan video tutorial dapat diakses secara online.',
                    'links' => [
                        ['title' => 'Lihat Materi', 'url' => '/materi']
                    ]
                ],
                [
                    'question' => 'Apakah materi akan diupdate?',
                    'answer' => 'Ya, materi pelatihan akan diperbarui secara berkala sesuai dengan perkembangan aplikasi dan feedback dari peserta pelatihan.',
                ],
                [
                    'question' => 'Bisakah saya mengakses materi setelah pelatihan selesai?',
                    'answer' => 'Tentu saja. Semua materi pelatihan akan tetap tersedia untuk referensi peserta bahkan setelah pelatihan selesai.',
                ]
            ]
        ];

        $tutorials = [
            [
                'title' => 'Pengenalan Wilkerstat SE2026',
                'description' => 'Video pengenalan aplikasi Wilkerstat dan fitur-fitur utamanya.',
                'thumbnail' => 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
                'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
                'duration' => '15:30',
                'level' => 'beginner',
                'views' => 1250
            ],
            [
                'title' => 'Import dan Validasi Data',
                'description' => 'Cara mengimpor data sensus dan melakukan validasi data di Wilkerstat.',
                'thumbnail' => 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
                'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
                'duration' => '22:45',
                'level' => 'intermediate',
                'views' => 890
            ],
            [
                'title' => 'Analisis dan Pelaporan',
                'description' => 'Teknik analisis data dan pembuatan laporan menggunakan Wilkerstat.',
                'thumbnail' => 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
                'video_url' => 'https://www.youtube.com/embed/dQw4w9WgXcQ',
                'duration' => '28:15',
                'level' => 'advanced',
                'views' => 654
            ]
        ];

        $downloads = [
            'Manual & Panduan' => [
                [
                    'name' => 'Manual Pengguna Wilkerstat SE2026',
                    'description' => 'Panduan lengkap penggunaan aplikasi Wilkerstat untuk pengguna.',
                    'size' => '5.2 MB',
                    'type' => 'PDF',
                    'icon' => 'fas fa-file-pdf',
                    'url' => 'https://bps.go.id/downloads/manual-wilkerstat.pdf',
                    'downloads' => 2340
                ],
                [
                    'name' => 'Quick Reference Guide',
                    'description' => 'Panduan singkat fitur-fitur utama Wilkerstat.',
                    'size' => '1.8 MB',
                    'type' => 'PDF',
                    'icon' => 'fas fa-file-pdf',
                    'url' => 'https://bps.go.id/downloads/quick-reference.pdf',
                    'downloads' => 1890
                ]
            ],
            'Template & Form' => [
                [
                    'name' => 'Template Laporan SE2026',
                    'description' => 'Template standar untuk laporan hasil sensus ekonomi.',
                    'size' => '890 KB',
                    'type' => 'DOCX',
                    'icon' => 'fas fa-file-word',
                    'url' => 'https://bps.go.id/downloads/template-laporan.docx',
                    'downloads' => 1456
                ],
                [
                    'name' => 'Form Evaluasi Pelatihan',
                    'description' => 'Form untuk evaluasi dan feedback pelatihan.',
                    'size' => '245 KB',
                    'type' => 'PDF',
                    'icon' => 'fas fa-file-pdf',
                    'url' => 'https://bps.go.id/downloads/form-evaluasi.pdf',
                    'downloads' => 987
                ]
            ],
            'Software & Tools' => [
                [
                    'name' => 'Wilkerstat SE2026 Installer',
                    'description' => 'Installer aplikasi Wilkerstat versi terbaru.',
                    'size' => '125 MB',
                    'type' => 'EXE',
                    'icon' => 'fas fa-download',
                    'url' => 'https://bps.go.id/downloads/wilkerstat-installer.exe',
                    'downloads' => 3456
                ],
                [
                    'name' => 'Data Validator Tool',
                    'description' => 'Tool tambahan untuk validasi data sebelum import.',
                    'size' => '15.6 MB',
                    'type' => 'ZIP',
                    'icon' => 'fas fa-file-archive',
                    'url' => 'https://bps.go.id/downloads/validator-tool.zip',
                    'downloads' => 876
                ]
            ]
        ];

        $team = [
            [
                'name' => 'Dr. Ahmad Susanto',
                'role' => 'Lead Trainer',
                'specialization' => 'Statistik Ekonomi & Metodologi Sensus',
                'email' => '<EMAIL>',
                'phone' => '+62-21-3841195',
                'avatar' => 'https://ui-avatars.com/api/?name=Ahmad+Susanto&background=3b82f6&color=fff&size=200',
                'status' => 'online'
            ],
            [
                'name' => 'Siti Rahayu, M.Stat',
                'role' => 'Technical Support',
                'specialization' => 'Aplikasi Wilkerstat & Troubleshooting',
                'email' => '<EMAIL>',
                'avatar' => 'https://ui-avatars.com/api/?name=Siti+Rahayu&background=10b981&color=fff&size=200',
                'status' => 'online'
            ],
            [
                'name' => 'Budi Santoso, S.Si',
                'role' => 'Data Analyst',
                'specialization' => 'Analisis Data & Validasi',
                'email' => '<EMAIL>',
                'avatar' => 'https://ui-avatars.com/api/?name=Budi+Santoso&background=f59e0b&color=fff&size=200',
                'status' => 'busy'
            ],
            [
                'name' => 'Maya Sari, M.Kom',
                'role' => 'IT Support',
                'specialization' => 'Infrastruktur & Sistem',
                'email' => '<EMAIL>',
                'avatar' => 'https://ui-avatars.com/api/?name=Maya+Sari&background=8b5cf6&color=fff&size=200',
                'status' => 'online'
            ]
        ];

        return view('training.contact', compact('contact', 'faqs', 'tutorials', 'downloads', 'team'));
    }

    /**
     * Display detailed pendahuluan material
     */
    public function pendahuluan()
    {
        // Get the detailed pendahuluan content from materials data
        $pendahuluanData = [
            'title' => '01 Pendahuluan Wilkerstat SE2026',
            'subtitle' => 'Pemutakhiran Kerangka Geospasial dan Muatan Wilkerstat SE2026',
            'description' => 'Materi pengenalan komprehensif tentang latar belakang, tujuan, dan komponen-komponen penting dalam kegiatan pemutakhiran Wilkerstat SE2026.',
            'pdf_link' => asset('bahanajar/01 Pendahuluan.pdf'),
            'duration' => '45 menit',
            'difficulty' => 'Pemula',
            'sections' => [
                'latar_belakang' => [
                    'title' => 'Latar Belakang',
                    'icon' => 'fas fa-info-circle',
                    'content' => [
                        'Satuan Lingkungan Setempat (SLS) adalah wilayah di bawah desa/kelurahan yang memiliki ketua dan pengurus yang telah operasional dan diakui oleh pemerintah desa/kelurahan.',
                        'Tantangan utama SLS adalah sifatnya yang sangat dinamis, sehingga memerlukan pemutakhiran sebelum pelaksanaan Sensus Ekonomi 2026 (SE2026).',
                        'Kegiatan Pemutakhiran Kerangka Geospasial dan Muatan Wilkerstat SE2026 dilakukan pada tahun 2025 dengan dua tahapan:'
                    ],
                    'tahapan' => [
                        'lapangan' => [
                            'title' => 'Kegiatan Lapangan',
                            'icon' => 'fas fa-map-marked-alt',
                            'items' => [
                                'Pemutakhiran peta dan SLS',
                                'Geotagging batas SLS dan kawasan ekonomi',
                                'Pembentukan sub-SLS'
                            ]
                        ],
                        'pengolahan' => [
                            'title' => 'Kegiatan Pengolahan',
                            'icon' => 'fas fa-cogs',
                            'items' => [
                                'Pengolahan master dan muatan SLS',
                                'Pengolahan peta wilkerstat hasil kegiatan lapangan'
                            ]
                        ]
                    ],
                    'kesimpulan' => 'Latar belakang menjelaskan pentingnya pemutakhiran SLS karena sifatnya yang dinamis, dengan kegiatan SE2026 yang terdiri dari tahapan lapangan (pemutakhiran peta, geotagging, pembentukan sub-SLS) dan pengolahan (master, muatan, dan peta wilkerstat) untuk mendukung Sensus Ekonomi 2026.'
                ],
                'maksud_tujuan' => [
                    'title' => 'Maksud dan Tujuan',
                    'icon' => 'fas fa-bullseye',
                    'content' => [
                        'Mendapatkan muatan wilkerstat yang seragam dan mutakhir',
                        'Mendapatkan kerangka geospasial yang mutakhir',
                        'Mendapatkan informasi mengenai wilayah konsentrasi ekonomi'
                    ],
                    'kesimpulan' => 'Tujuan kegiatan adalah menghasilkan data wilkerstat dan kerangka geospasial yang terbaru dan konsisten, serta informasi tentang wilayah konsentrasi ekonomi untuk mendukung pelaksanaan SE2026.'
                ],
                'landasan_hukum' => [
                    'title' => 'Landasan Hukum',
                    'icon' => 'fas fa-gavel',
                    'content' => 'Dokumen tidak memberikan rincian spesifik tentang landasan hukum, tetapi menyebutkan bahwa kegiatan ini memiliki dasar hukum yang mendukung pelaksanaan pemutakhiran wilkerstat.',
                    'kesimpulan' => 'Landasan hukum menjadi dasar formal untuk pelaksanaan kegiatan pemutakhiran, meskipun detail spesifik tidak diuraikan dalam dokumen.'
                ],
                'instrumen_perangkat' => [
                    'title' => 'Jenis Instrumen dan Perangkat',
                    'icon' => 'fas fa-tools',
                    'pengolahan_master' => [
                        'title' => 'Instrumen Pengolahan Master dan Muatan SLS',
                        'icon' => 'fas fa-database',
                        'items' => [
                            'Daftar Perubahan SLS (Daftar PSLS): Berisi informasi SLS yang mengalami perubahan dari hasil pemutakhiran di lapangan',
                            'SE2026-WILKERSTAT.RS: Daftar Rekap SLS hasil pemutakhiran, memuat informasi muatan tiap SLS/Non-SLS per desa/kelurahan',
                            'Aplikasi Frame Register: Digunakan untuk pengelolaan data SLS'
                        ]
                    ],
                    'pengolahan_peta' => [
                        'title' => 'Instrumen Pengolahan Peta',
                        'icon' => 'fas fa-map',
                        'items' => [
                            'Peta digital SLS kondisi terbaru: Dapat diunduh dari Geospatial System (GS) di https://dataspasial.bps.go.id/gs (periode 2024.1) atau versi terbaru dari BPS Kabupaten/Kota',
                            'Geotagging batas SLS: Melalui https://wilkerstat.bps.go.id untuk informasi lokasi dan perubahan batas SLS',
                            'Peta WS hasil pemutakhiran di lapangan: Memberikan informasi perbaikan batas SLS',
                            'Lembar Kerja Peta (LK-Peta): Hasil penggambaran sketsa peta untuk di-scan',
                            'Master SLS Snapshot Tahun 2025 Semester 1: Data dasar untuk pemutakhiran'
                        ]
                    ],
                    'software_aplikasi' => [
                        'title' => 'Software dan Aplikasi',
                        'icon' => 'fas fa-laptop-code',
                        'items' => [
                            'QGIS: Digunakan untuk editing peta digital',
                            'Geospatial System (GS): Untuk pengiriman, validasi, dan penyimpanan peta digital hasil kegiatan',
                            'Aplikasi pendukung lainnya: Alat bantu untuk pemrosesan dan pengolahan peta, tersedia untuk diunduh gratis'
                        ]
                    ],
                    'kesimpulan' => 'Instrumen dan perangkat mencakup dokumen (Daftar PSLS, SE2026-WILKERSTAT.RS, LK-Peta), aplikasi (Frame Register, GS), dan software (QGIS) untuk mendukung pengolahan master, muatan SLS, dan peta wilkerstat, dengan akses ke data terbaru melalui platform online.'
                ],
                'jadwal_kegiatan' => [
                    'title' => 'Jadwal Kegiatan',
                    'icon' => 'fas fa-calendar-alt',
                    'content' => 'Dokumen menyebutkan adanya jadwal kegiatan untuk tahun 2025, tetapi tidak memberikan rincian spesifik dalam teks yang tersedia.',
                    'kesimpulan' => 'Jadwal kegiatan disusun untuk mengatur pelaksanaan pemutakhiran wilkerstat pada tahun 2025, meskipun detailnya tidak diuraikan dalam dokumen ini.',
                    'reference_link' => route('schedule'),
                    'reference_text' => 'Lihat jadwal pelatihan lengkap'
                ]
            ]
        ];

        return view('training.pendahuluan', compact('pendahuluanData'));
    }

    /**
     * Display detailed organisasi pengolahan material
     */
    public function organisasiPengolahan()
    {
        $organisasiData = [
            'title' => '02 Organisasi Pengolahan Wilkerstat SE2026',
            'subtitle' => 'Struktur Organisasi dan Tanggung Jawab dalam Pengolahan',
            'description' => 'Materi komprehensif tentang struktur organisasi, peran, dan tanggung jawab dalam kegiatan pengolahan Pemutakhiran Kerangka Geospasial dan Muatan Wilkerstat SE2026.',
            'pdf_link' => asset('bahanajar/02 Organisasi Pengolahan.pdf'),
            'duration' => '45 menit',
            'difficulty' => 'Menengah',
            'sections' => [
                'penanggung_jawab' => [
                    'title' => 'Penanggung Jawab Kabupaten/Kota',
                    'icon' => 'fas fa-user-tie',
                    'content' => [
                        'Mengomunikasikan kegiatan lapangan Pemutakhiran Kerangka Geospasial dan Muatan Wilkerstat SE2026 ke pemerintah daerah.'
                    ],
                    'kesimpulan' => 'Penanggung Jawab Kabupaten/Kota bertugas sebagai penghubung dengan pemerintah daerah untuk memastikan koordinasi yang efektif terkait kegiatan lapangan pemutakhiran Wilkerstat SE2026.'
                ],
                'tim_wilkerstat' => [
                    'title' => 'Tim Wilkerstat BPS Kabupaten/Kota',
                    'icon' => 'fas fa-users',
                    'content' => [
                        'Mengatur alokasi wilayah petugas lapangan untuk Pemutakhiran Kerangka Geospasial dan Muatan Wilkerstat SE2026.',
                        'Menyiapkan instrumen yang diperlukan untuk kegiatan lapangan.',
                        'Mengatur pembagian instrumen untuk kegiatan lapangan.',
                        'Memantau secara berkala progress geotagging melalui https://wilkerstat.bps.go.id.',
                        'Mengatur pengumpulan dokumen dan peta hasil kegiatan lapangan.',
                        'Memeriksa kelengkapan dokumen dan peta hasil kegiatan pada tingkat kabupaten/kota.',
                        'Bertanggung jawab atas aspek teknis dan administrasi terkait pengolahan master dan peta wilkerstat.',
                        'Mengatur penugasan atau alokasi petugas pengolah muatan dan peta.',
                        'Menerima semua dokumen dari pelaksanaan lapangan dan memeriksa kelengkapannya (receiving).',
                        'Mendistribusikan dokumen pemutakhiran master wilkerstat dan peta kepada petugas pengolahan.',
                        'Bertanggung jawab terhadap kegiatan entri pengajuan perubahan master wilkerstat melalui aplikasi FRS-MFDOnline.',
                        'Melakukan monitoring dan approval pengajuan perubahan master wilkerstat dan Daftar PSLS yang telah dientri petugas pengolah melalui aplikasi FRS-MFDOnline.',
                        'Bertanggung jawab dalam pelaksanaan pengolahan muatan, termasuk monitoring dan pemeriksaan hasil entri melalui aplikasi SiPW.',
                        'Bertanggung jawab dalam pelaksanaan pengolahan peta.',
                        'Memandu petugas pengolah peta dalam hal instalasi software dan penyelesaian permasalahan teknis.',
                        'Melakukan pengawasan dan pemeriksaan peta digital hasil pengolahan peta.',
                        'Melakukan upload hasil pengolahan peta melalui Geospatial System (https://dataspasial.bps.go.id/gs).',
                        'Mengakses Geospatial System untuk monitoring dan pengiriman hasil pengolahan peta.',
                        'Menerima kembali dokumen pemutakhiran master wilkerstat dan peta dari petugas pengolahan.',
                        'Menyimpan kembali dokumen pemutakhiran master wilkerstat dan peta yang telah selesai dientri oleh petugas pengolah/operator ke tempat penyimpanan dokumen.',
                        'Membuat layout peta.'
                    ],
                    'kesimpulan' => 'Tim Wilkerstat BPS Kabupaten/Kota memiliki tanggung jawab menyeluruh dalam pengelolaan kegiatan lapangan dan pengolahan, mulai dari alokasi wilayah, penyediaan instrumen, monitoring geotagging, pengelolaan dokumen, entri data melalui FRS-MFDOnline dan SiPW, hingga pengolahan dan pengawasan peta digital, termasuk penyimpanan dokumen dan pembuatan layout peta.'
                ],
                'petugas_pengolahan' => [
                    'title' => 'Petugas Pengolahan',
                    'icon' => 'fas fa-user-cog',
                    'content' => [
                        'Menerima penugasan dari admin kabupaten/kota.',
                        'Menerima dokumen pemutakhiran master wilkerstat dari admin kabupaten/kota.',
                        'Melakukan entri muatan dokumen SE2026-WILKERSTAT.RS melalui aplikasi SiPW.',
                        'Mengembalikan dokumen yang telah dientri kepada admin kabupaten/kota.',
                        'Mendigitasi batas SLS/non-SLS berdasarkan peta hasil lapangan.',
                        'Membuat peta desa dan kecamatan berdasarkan batas SLS/non-SLS hasil perubahan.'
                    ],
                    'kesimpulan' => 'Petugas Pengolahan bertugas melaksanakan tugas operasional seperti entri data muatan melalui SiPW, digitasi batas SLS/non-SLS, dan pembuatan peta desa/kecamatan, dengan koordinasi langsung dari admin kabupaten/kota.'
                ],
                'catatan_tambahan' => [
                    'title' => 'Catatan Tambahan',
                    'icon' => 'fas fa-sticky-note',
                    'content' => [
                        'Pastikan akses ke aplikasi SiPW (https://sipw.bps.go.id) dan Geospatial System (https://dataspasial.bps.go.id/gs) tersedia untuk petugas pengolahan.',
                        'FRS-MFDOnline digunakan untuk entri dan approval perubahan master wilkerstat dan Daftar PSLS.',
                        'Dokumen dan peta hasil pengolahan harus disimpan dengan baik oleh Tim Wilkerstat BPS Kabupaten/Kota.',
                        'Untuk detail jadwal pelatihan, lihat dokumen "Jadwal Pelatihan Petugas Pengolahan Wilkerstat.xlsx".'
                    ],
                    'reference_link' => route('schedule'),
                    'reference_text' => 'Lihat jadwal pelatihan lengkap'
                ]
            ]
        ];

        return view('training.organisasi-pengolahan', compact('organisasiData'));
    }

    /**
     * Display detailed mekanisme pengolahan material
     */
    public function mekanismePengolahan()
    {
        $mekanismeData = [
            'title' => '03 Mekanisme Pengolahan Wilkerstat SE2026',
            'subtitle' => 'Prosedur dan Mekanisme Pengolahan Data Secara Sistematis',
            'description' => 'Materi komprehensif tentang mekanisme dan prosedur pengolahan data dalam kegiatan Pemutakhiran Kerangka Geospasial dan Muatan Wilkerstat SE2026.',
            'pdf_link' => asset('bahanajar/03 Mekanisme Pengolahan.pdf'),
            'duration' => '60 menit',
            'difficulty' => 'Menengah',
            'sections' => [
                'mekanisme_umum' => [
                    'title' => 'Mekanisme Pengolahan Secara Umum',
                    'icon' => 'fas fa-cogs',
                    'content' => [
                        'Pengolahan data wilkerstat dilakukan secara bertahap dan sistematis untuk memastikan kualitas dan konsistensi data.',
                        'Proses pengolahan melibatkan validasi, verifikasi, dan standardisasi data sesuai dengan pedoman yang telah ditetapkan.',
                        'Setiap tahapan pengolahan memiliki checkpoint untuk memastikan kualitas output sebelum melanjutkan ke tahap berikutnya.'
                    ],
                    'tahapan' => [
                        'persiapan' => [
                            'title' => 'Tahap Persiapan',
                            'icon' => 'fas fa-clipboard-list',
                            'items' => [
                                'Penyiapan dokumen dan instrumen pengolahan',
                                'Verifikasi kelengkapan data hasil lapangan',
                                'Alokasi tugas kepada petugas pengolahan'
                            ]
                        ],
                        'pengolahan' => [
                            'title' => 'Tahap Pengolahan',
                            'icon' => 'fas fa-database',
                            'items' => [
                                'Entri data melalui aplikasi yang telah ditentukan',
                                'Validasi dan verifikasi data',
                                'Pemeriksaan konsistensi dan kelengkapan'
                            ]
                        ],
                        'finalisasi' => [
                            'title' => 'Tahap Finalisasi',
                            'icon' => 'fas fa-check-circle',
                            'items' => [
                                'Review dan approval hasil pengolahan',
                                'Penyimpanan dan backup data',
                                'Penyusunan laporan hasil pengolahan'
                            ]
                        ]
                    ],
                    'kesimpulan' => 'Mekanisme pengolahan secara umum mengikuti alur sistematis dari persiapan, pengolahan, hingga finalisasi dengan checkpoint kualitas di setiap tahapan untuk memastikan hasil yang akurat dan konsisten.'
                ],
                'master_wilkerstat' => [
                    'title' => 'Master Wilkerstat dan Muatan SLS',
                    'icon' => 'fas fa-database',
                    'pengolahan_master' => [
                        'title' => 'Pengolahan Master Wilkerstat',
                        'icon' => 'fas fa-server',
                        'content' => [
                            'Master wilkerstat merupakan database utama yang berisi informasi lengkap tentang Satuan Lingkungan Setempat (SLS).',
                            'Pemutakhiran master dilakukan berdasarkan hasil kegiatan lapangan yang telah diverifikasi.',
                            'Proses pengolahan menggunakan aplikasi FRS-MFDOnline untuk entri dan approval perubahan data.'
                        ],
                        'komponen' => [
                            'Kode dan nama SLS',
                            'Batas wilayah administratif',
                            'Koordinat geografis',
                            'Status operasional SLS',
                            'Informasi demografis dasar'
                        ]
                    ],
                    'muatan_sls' => [
                        'title' => 'Pengolahan Muatan SLS',
                        'icon' => 'fas fa-chart-bar',
                        'content' => [
                            'Muatan SLS berisi informasi detail tentang karakteristik ekonomi dan sosial dalam setiap SLS.',
                            'Data muatan diperoleh dari dokumen SE2026-WILKERSTAT.RS hasil kegiatan lapangan.',
                            'Entri data muatan dilakukan melalui aplikasi SiPW (Sistem Pemutakhiran Wilkerstat).'
                        ],
                        'komponen' => [
                            'Jumlah rumah tangga',
                            'Jumlah usaha/perusahaan',
                            'Jenis kegiatan ekonomi dominan',
                            'Fasilitas umum dan sosial',
                            'Infrastruktur wilayah'
                        ]
                    ],
                    'aplikasi_pendukung' => [
                        'title' => 'Aplikasi Pendukung',
                        'icon' => 'fas fa-laptop-code',
                        'frs_mfd' => [
                            'name' => 'FRS-MFDOnline',
                            'fungsi' => 'Entri dan approval perubahan master wilkerstat',
                            'url' => 'Akses melalui portal BPS internal'
                        ],
                        'sipw' => [
                            'name' => 'SiPW (Sistem Pemutakhiran Wilkerstat)',
                            'fungsi' => 'Entri data muatan SLS',
                            'url' => 'https://sipw.bps.go.id'
                        ]
                    ],
                    'kesimpulan' => 'Pengolahan master wilkerstat dan muatan SLS menggunakan dua aplikasi utama: FRS-MFDOnline untuk master dan SiPW untuk muatan, dengan data yang saling terintegrasi untuk menghasilkan informasi wilkerstat yang komprehensif.'
                ],
                'alur_pengolahan' => [
                    'title' => 'Alur Pengolahan Data',
                    'icon' => 'fas fa-project-diagram',
                    'tahapan_detail' => [
                        [
                            'step' => 1,
                            'title' => 'Penerimaan Dokumen',
                            'description' => 'Tim Wilkerstat menerima dan memeriksa kelengkapan dokumen hasil lapangan',
                            'output' => 'Dokumen terverifikasi dan siap untuk diproses'
                        ],
                        [
                            'step' => 2,
                            'title' => 'Distribusi Tugas',
                            'description' => 'Pembagian dokumen kepada petugas pengolahan sesuai alokasi wilayah',
                            'output' => 'Petugas mendapat penugasan dan dokumen kerja'
                        ],
                        [
                            'step' => 3,
                            'title' => 'Entri Data Master',
                            'description' => 'Petugas melakukan entri perubahan master melalui FRS-MFDOnline',
                            'output' => 'Data master terupdate dalam sistem'
                        ],
                        [
                            'step' => 4,
                            'title' => 'Entri Data Muatan',
                            'description' => 'Petugas melakukan entri data muatan melalui aplikasi SiPW',
                            'output' => 'Data muatan SLS tersimpan dalam sistem'
                        ],
                        [
                            'step' => 5,
                            'title' => 'Validasi dan Verifikasi',
                            'description' => 'Tim Wilkerstat melakukan pemeriksaan dan approval data',
                            'output' => 'Data tervalidasi dan disetujui'
                        ],
                        [
                            'step' => 6,
                            'title' => 'Penyimpanan Dokumen',
                            'description' => 'Dokumen fisik dikembalikan dan disimpan dengan baik',
                            'output' => 'Dokumen tersimpan untuk referensi dan audit'
                        ]
                    ],
                    'kesimpulan' => 'Alur pengolahan data mengikuti 6 tahapan sistematis dari penerimaan dokumen hingga penyimpanan, dengan validasi di setiap tahap untuk memastikan kualitas dan akurasi data.'
                ],
                'quality_control' => [
                    'title' => 'Kontrol Kualitas',
                    'icon' => 'fas fa-shield-alt',
                    'prinsip' => [
                        'Verifikasi ganda pada setiap tahapan entri data',
                        'Validasi silang antara data master dan muatan',
                        'Pemeriksaan konsistensi dengan data periode sebelumnya',
                        'Approval berjenjang untuk perubahan signifikan'
                    ],
                    'tools' => [
                        'Built-in validation dalam aplikasi FRS-MFDOnline dan SiPW',
                        'Laporan error dan warning otomatis',
                        'Dashboard monitoring progress pengolahan',
                        'Sistem backup dan recovery data'
                    ],
                    'kesimpulan' => 'Kontrol kualitas dilakukan melalui multiple layer validation, tools otomatis, dan approval berjenjang untuk memastikan akurasi dan konsistensi data wilkerstat.'
                ]
            ]
        ];

        return view('training.mekanisme-pengolahan', compact('mekanismeData'));
    }

    /**
     * Display detailed SiPW SE2026 material
     */
    public function sipwSe2026()
    {
        $sipwData = [
            'title' => '04.2a Sistem Pemutakhiran Wilkerstat (SiPW) SE2026',
            'subtitle' => 'Panduan Lengkap Penggunaan Aplikasi SiPW',
            'description' => 'Materi komprehensif tentang tata cara pengoperasian Sistem Pemutakhiran Wilkerstat (SiPW) untuk mendukung kegiatan SE2026, termasuk login, dashboard, administrasi pengguna, dan entri dokumen.',
            'pdf_link' => asset('bahanajar/04.2a Sistem Pemutakhiran Wilkerstat (SiPW) SE2026 v.2.pdf'),
            'duration' => '90 menit',
            'difficulty' => 'Lanjutan',
            'sections' => [
                'penjelasan_umum' => [
                    'title' => 'Penjelasan Umum',
                    'icon' => 'fas fa-info-circle',
                    'content' => [
                        'tujuan_ruang_lingkup' => [
                            'title' => 'Tujuan dan Ruang Lingkup',
                            'items' => [
                                'Mengolah data Rekap SLS (SE2026-WILKERSTAT.RS) di kabupaten/kota seluruh Indonesia',
                                'Tujuan: Mendapatkan informasi muatan sub-SLS yang termutakhirkan sebagai indikator perhitungan beban petugas untuk Sensus Ekonomi 2026',
                                'Membentuk sub-SLS sesuai hasil Rekap SLS'
                            ]
                        ],
                        'petugas_pengolah' => [
                            'title' => 'Petugas Pengolah',
                            'items' => [
                                'Petugas Receiving Batching: Tim Wilkerstat BPS Kabupaten/Kota',
                                'Operator Entri Dokumen: Petugas pengolahan muatan Wilkerstat SE2026 BPS Kabupaten/Kota',
                                'Pengawas Pengolahan: Tim Wilkerstat atau petugas organik yang ditugaskan BPS Kabupaten/Kota'
                            ]
                        ]
                    ],
                    'kesimpulan' => 'Penjelasan umum menyoroti tujuan pengolahan data Rekap SLS untuk mendukung SE2026, dengan fokus pada pembentukan sub-SLS dan peran petugas seperti receiving batching, entri dokumen, dan pengawasan oleh tim BPS Kabupaten/Kota.'
                ],
                'tata_cara_pengoperasian' => [
                    'title' => 'Tata Cara Pengoperasian SiPW',
                    'icon' => 'fas fa-desktop',
                    'content' => [
                        'login' => [
                            'title' => 'Login',
                            'description' => 'Proses login ke aplikasi SiPW (https://sipw.bps.go.id)',
                            'steps' => [
                                'Akses aplikasi SiPW melalui browser',
                                'Masukkan kredensial yang telah diberikan',
                                'Verifikasi akses sesuai dengan role pengguna'
                            ]
                        ],
                        'dashboard' => [
                            'title' => 'Dashboard',
                            'description' => 'Interface utama untuk monitoring dan navigasi',
                            'features' => [
                                'Menampilkan tabel per wilayah dengan persentase tiap kategori monitoring',
                                'Rekap monitoring berjalan secara otomatis setiap jam',
                                'Wilayah dapat dipilih dari level provinsi hingga SLS'
                            ]
                        ],
                        'administrasi_pengguna' => [
                            'title' => 'Administrasi Pengguna',
                            'roles' => [
                                'Admin: Mengelola pengguna web',
                                'Petugas Pengolahan: Memiliki akses ke menu entri dan sinkronisasi dengan SOBAT BPS'
                            ]
                        ],
                        'assign_wilayah' => [
                            'title' => 'Assign Wilayah Kerja',
                            'process' => [
                                'Admin dapat mengunduh template "Assign.xlsx" untuk mengisi informasi petugas dan pengawas',
                                'Setelah diisi, file diunggah ke SiPW, dan status berubah menjadi "Sudah Assign"'
                            ]
                        ],
                        'generate_dokumen' => [
                            'title' => 'Generate Dokumen',
                            'features' => [
                                'Klik "Generate" untuk memproses pembentukan file PDF di server dan mengunggahnya ke Server S3',
                                'Fitur "Dokumen Kosong" memungkinkan unduh file PDF tanpa informasi petugas/SLS',
                                'Fitur "Download All" mengunduh file terpilih dengan jeda 5 detik antar file'
                            ]
                        ],
                        'alur_cetak' => [
                            'title' => 'Alur Cetak Dokumen',
                            'description' => 'Melibatkan sinkronisasi data petugas, penugasan SLS, dan pembuatan dokumen melalui fitur "Generate"'
                        ]
                    ],
                    'kesimpulan' => 'Tata cara pengoperasian SiPW mencakup login, penggunaan dashboard untuk monitoring, administrasi pengguna, penugasan wilayah kerja melalui template Assign.xlsx, dan pembuatan dokumen PDF dengan fitur generate, download, dan sinkronisasi data.'
                ],
                'tata_cara_entri' => [
                    'title' => 'Tata Cara Entri Dokumen',
                    'icon' => 'fas fa-edit',
                    'content' => [
                        'proses_entri' => [
                            'title' => 'Proses Entri',
                            'description' => 'Entri dokumen dilakukan untuk dokumen SiPW.RS (berbasis SE2026-WILKERSTAT.RS) melalui aplikasi SiPW',
                            'steps' => [
                                'Akses menu entri dokumen dalam aplikasi SiPW',
                                'Pilih dokumen yang akan dientry',
                                'Lakukan input data sesuai dengan format yang telah ditentukan',
                                'Verifikasi data yang telah diinput',
                                'Simpan data dan tunggu konfirmasi sistem'
                            ]
                        ],
                        'konfirmasi' => [
                            'title' => 'Konfirmasi Penyimpanan',
                            'description' => 'Setelah proses entri selesai, sistem akan menampilkan pesan "berhasil menyimpan data"'
                        ]
                    ],
                    'kesimpulan' => 'Tata cara entri dokumen berfokus pada proses entri data SiPW.RS melalui aplikasi SiPW, dengan konfirmasi keberhasilan penyimpanan data setelah proses selesai.'
                ],
                'catatan_tambahan' => [
                    'title' => 'Catatan Tambahan',
                    'icon' => 'fas fa-sticky-note',
                    'content' => [
                        'aplikasi_sipw' => [
                            'title' => 'Aplikasi SiPW',
                            'description' => 'Aplikasi SiPW (https://sipw.bps.go.id) digunakan untuk entri, monitoring, dan pembuatan dokumen'
                        ],
                        'sinkronisasi' => [
                            'title' => 'Sinkronisasi SOBAT BPS',
                            'description' => 'Sinkronisasi dengan SOBAT BPS diperlukan untuk administrasi petugas pengolahan'
                        ],
                        'template' => [
                            'title' => 'Template Assign',
                            'description' => 'Template Assign.xlsx digunakan untuk penugasan wilayah kerja'
                        ],
                        'server_s3' => [
                            'title' => 'Server S3',
                            'description' => 'Pastikan file PDF yang dihasilkan melalui fitur "Generate" tersimpan di Server S3'
                        ]
                    ],
                    'kesimpulan' => 'Catatan tambahan mencakup informasi penting tentang akses aplikasi, sinkronisasi data, penggunaan template, dan penyimpanan file untuk memastikan kelancaran operasional SiPW.'
                ]
            ]
        ];

        return view('training.sipw-se2026', compact('sipwData'));
    }

    /**
     * Display detailed pengolahan peta material
     */
    public function pengolahanPeta()
    {
        $pengolahanPetaData = [
            'title' => '05 Pengolahan Peta Wilkerstat SE2026',
            'subtitle' => 'Teknik dan Metode Pengolahan Peta Digital untuk Keperluan Wilkerstat',
            'description' => 'Materi komprehensif tentang pengolahan peta digital menggunakan QGIS, mulai dari persiapan alat dan bahan, georeferensi, editing, hingga validasi peta untuk mendukung kegiatan SE2026.',
            'pdf_link' => asset('bahanajar/05 Pengolahan Peta.pdf'),
            'duration' => '75 menit',
            'difficulty' => 'Lanjutan',
            'sections' => [
                'alat_bahan' => [
                    'title' => 'Alat dan Bahan',
                    'icon' => 'fas fa-tools',
                    'content' => [
                        'alat_digunakan' => [
                            'title' => 'Alat yang Digunakan',
                            'items' => [
                                'Aplikasi QGIS: Perangkat lunak berbasis desktop untuk mengolah peta digital',
                                'Plugin QGIS: Freehand Raster Georeferencer, QuickMapServices, QR Barcode Layout Item, Clipper, Topology Checker, Dissect/Dissolve Overlaps (SAGA NextGen)',
                                'Web Wilkerstat: Mengunduh data geotagging hasil kegiatan lapangan',
                                'Geospatial System: Sistem berbasis web untuk mengunduh peta',
                                'Aplikasi Rename Peta WS: Bulk Rename Utility untuk penggantian nama file secara massal',
                                'Processing Tools: Plugin dalam QGIS untuk memproses dan menganalisis data geospasial'
                            ]
                        ],
                        'bahan_instrumen' => [
                            'title' => 'Bahan/Instrumen yang Digunakan',
                            'items' => [
                                'Template Layout Peta: Diunduh dari Geospatial System',
                                'Peta Digital SLS: Diunduh dari Geospatial System (periode 2024.1) atau versi terbaru dari BPS Kabupaten/Kota',
                                'Peta WS Hasil Lapangan: Dari kegiatan lapangan Wilkerstat',
                                'LK-Peta: Dari kegiatan lapangan Wilkerstat',
                                'Geotagging Batas SLS: Diunduh melalui website Wilkerstat',
                                'Master SLS: Diunduh dari aplikasi SiPW'
                            ]
                        ]
                    ],
                    'kesimpulan' => 'Alat dan bahan untuk pengolahan peta mencakup QGIS dengan berbagai plugin (Freehand Raster Georeferencer, QuickMapServices, Clipper, dll.), aplikasi rename, dan sistem web seperti Geospatial System dan Web Wilkerstat, serta bahan seperti peta digital SLS, Peta WS, LK-Peta, geotagging, dan Master SLS.'
                ],
                'penyiapan_pengolahan' => [
                    'title' => 'Penyiapan Pengolahan Peta',
                    'icon' => 'fas fa-cog',
                    'content' => [
                        'unduh_peta_sls' => [
                            'title' => 'Mengunduh Peta SLS dari Geospatial System',
                            'steps' => [
                                'Pilih Peta Digital, periode 2024-1, provinsi sesuai wilayah, level SLS/Non-SLS, sumber Final, format GeoJSON',
                                'Klik Cari, unduh per kabupaten, dan konfirmasi unduhan'
                            ]
                        ],
                        'instalasi_plugin' => [
                            'title' => 'Instalasi Plugin QGIS',
                            'plugins' => [
                                'Plugin QuickMapServices: Pilih Plugins → Manage and Install Plugins → Cari → Install',
                                'Plugin QR Barcode Layout Item: Pilih Plugins → Manage and Install Plugins → Cari → Install',
                                'Plugin Clipper: Pilih Plugins → Manage and Install Plugins → Cari → Install',
                                'Plugin Dissect/Dissolve Overlaps (SAGA NextGen): Pilih Plugins → Manage and Install Plugins → Cari → Install',
                                'Plugin Topology Checker: Pilih Plugins → Manage and Install Plugins → Cari → Aktivasi dengan checklist'
                            ]
                        ],
                        'processing_tools' => [
                            'title' => 'Menambahkan Processing Tools',
                            'steps' => [
                                'Klik kanan pada Toolbar, aktifkan Processing Toolbox Panel',
                                'Tambahkan tools dari folder 01-Input + 09_Processing Tools: Cek_Validitas, Fill_Gaps, Cek_Master_PetaSLS, Dissolve_Desa_Kec',
                                'Tools ditampilkan di Models → Wilkerstat2025'
                            ]
                        ]
                    ],
                    'kesimpulan' => 'Penyiapan pengolahan peta melibatkan pengunduhan peta digital SLS dari Geospatial System dalam format GeoJSON dan instalasi plugin QGIS seperti QuickMapServices, Clipper, dan Topology Checker, serta penambahan Processing Tools untuk validasi dan pengolahan peta.'
                ],
                'penyiapan_bahan' => [
                    'title' => 'Penyiapan Bahan',
                    'icon' => 'fas fa-folder-open',
                    'content' => [
                        'unduh_geotagging' => [
                            'title' => 'Pengunduhan Geotagging Hasil Lapangan',
                            'steps' => [
                                'Buka https://wilkerstat.bps.go.id, pilih Data → Download',
                                'Parameter: Project (Updating SE2026), Jenis Data (Landmark), Level Cakupan (Kabupaten/Kota), Kategori (Semua Kategori), Status Project (Aktif), Status Landmark (Aktif), Jenis File (CSV)',
                                'Filter wilayah sesuai kebutuhan, lalu klik Download'
                            ]
                        ],
                        'penyiapan_master' => [
                            'title' => 'Penyiapan Master SLS',
                            'steps' => [
                                'Mengumpulkan dokumen PSLS, Peta WS, dan geotagging',
                                'Pengajuan perubahan SLS atau Perubahan Wilayah Administrasi (PWA) melalui FRS MFDOnline',
                                'Approval hasil entri pemutakhiran Master Wilkerstat',
                                'Monitoring progres pengolahan pemutakhiran Master Wilkerstat'
                            ]
                        ],
                        'scan_peta' => [
                            'title' => 'Scan Peta WS',
                            'requirements' => [
                                'Scan semua peta hasil lapangan dalam format JPEG, full color, resolusi 200 dpi',
                                'Pastikan kertas tidak terlipat, susun berdasarkan urutan ID'
                            ]
                        ],
                        'rename_peta' => [
                            'title' => 'Rename Peta WS',
                            'process' => [
                                'Gunakan Bulk Rename Utility untuk mengganti nama file sesuai file teks (ANSI/Unicode UTF-16)',
                                'Salin nama file hasil scan di Bulk Rename Utility (Clipboard Copy → Filename + Ext)',
                                'Di Excel, tempel nama file di kolom A, isi kolom B dengan nama baru berdasarkan IDSLS, gunakan fungsi CONCAT',
                                'Salin hasil ke Notepad/Notepad++, simpan sebagai file teks (idkab.txt)',
                                'Di Bulk Rename Utility, import file teks/CSV, pilih file, dan rename ke folder tujuan'
                            ]
                        ]
                    ],
                    'kesimpulan' => 'Penyiapan bahan melibatkan pengunduhan geotagging dari Web Wilkerstat, penyiapan Master SLS melalui FRS MFDOnline, scanning Peta WS dalam format JPEG, dan penggantian nama file menggunakan Bulk Rename Utility dengan file teks yang disusun melalui Excel.'
                ],
                'georeferensi_peta' => [
                    'title' => 'Georeferensi Peta yang Mengalami Perubahan',
                    'icon' => 'fas fa-map-marked-alt',
                    'content' => [
                        'georeferensi' => [
                            'title' => 'Georeferensi Peta',
                            'tujuan' => 'Menyediakan referensi sistem koordinat untuk Peta WS yang telah discan agar siap didigitasi',
                            'steps' => [
                                'Tambahkan gambar scan Peta WS di QGIS',
                                'Gunakan Plugin Freehand Raster Georeferencer: Pilih titik acuan pada Peta WS, geser ke titik sesuai pada peta digital SLS (2-3 titik), simpan hasil georeferensi'
                            ]
                        ],
                        'editing_peta' => [
                            'title' => 'Editing Peta Digital',
                            'tujuan' => 'Menyesuaikan batas SLS/sub-SLS pada peta digital dengan kondisi lapangan',
                            'tipe_koreksi' => [
                                'Non-Topologis: Memotong poligon (Split Features untuk pemekaran/pembentukan SLS baru), menggabungkan poligon (Merge Features), dan mengedit atribut',
                                'Topologis: Menghilangkan gap, overlap, dan duplikat geometri'
                            ]
                        ],
                        'cleaning_validasi' => [
                            'title' => 'Cleaning dan Validasi Peta',
                            'tujuan' => 'Menghilangkan error dan meningkatkan kualitas peta digital',
                            'tipe_koreksi' => [
                                'Null Geometry: Gunakan "Remove Null Geometries" di Processing Toolbox, simpan layer non-null sebagai *.gpkg',
                                'Invalid Geometry: Gunakan Cek_Validitas_Geometry untuk mendeteksi self-intersection, duplicate vertices, non-closed rings; perbaiki secara manual (potong/geser vertex)',
                                'Topology (Gap/Overlap): Gunakan Topology Checker dengan aturan (no gaps, no overlaps, no duplicates, no invalid geometries)',
                                'Overlaps: Perbaiki secara manual dengan Clipper atau massal dengan Dissect/Dissolve Overlaps (SAGA)',
                                'Gaps: Perbaiki secara manual dengan Avoid Overlap on Active Layer atau massal dengan Fill_Gaps',
                                'Validasi Atribut: Gunakan Cek_Master_PetaSLS untuk memeriksa kesesuaian atribut dengan Master SLS, perbaiki ID duplikat'
                            ]
                        ],
                        'ekspor_geojson' => [
                            'title' => 'Ekspor ke GeoJSON',
                            'process' => 'Ekspor shapefile bebas error ke GeoJSON dengan CRS 4326 dan atribut UTF-8 untuk diunggah ke Geospatial System'
                        ],
                        'dissolving' => [
                            'title' => 'Dissolving Peta Desa dan Kecamatan',
                            'process' => [
                                'Gunakan Dissolve_Desa_Kec untuk membentuk peta desa/kecamatan dari peta SLS/sub-SLS',
                                'Ekspor sebagai <idkab>_desa_2025.geojson, unggah ke Geospatial System'
                            ]
                        ]
                    ],
                    'kesimpulan' => 'Georeferensi peta melibatkan penyesuaian koordinat Peta WS menggunakan QGIS, editing peta digital (non-topologis dan topologis), cleaning error geometri/topologi menggunakan tools seperti Cek_Validitas dan Fill_Gaps, validasi atribut dengan Master SLS, dan ekspor ke GeoJSON untuk diunggah ke Geospatial System, serta pembentukan peta desa/kecamatan.'
                ],
                'catatan_tambahan' => [
                    'title' => 'Catatan Tambahan',
                    'icon' => 'fas fa-sticky-note',
                    'content' => [
                        'Aplikasi QGIS digunakan sebagai alat utama untuk pengolahan peta digital',
                        'Geospatial System (https://dataspasial.bps.go.id/gs) digunakan untuk mengunduh peta dan template, serta mengunggah hasil peta',
                        'Web Wilkerstat (https://wilkerstat.bps.go.id) digunakan untuk mengunduh geotagging',
                        'FRS MFDOnline digunakan untuk pengajuan perubahan SLS dan wilayah administrasi',
                        'Pastikan format file GeoJSON dengan CRS 4326 dan atribut UTF-8 untuk kompatibilitas dengan Geospatial System'
                    ],
                    'reference_link' => route('schedule'),
                    'reference_text' => 'Lihat jadwal pelatihan lengkap'
                ]
            ]
        ];

        return view('training.pengolahan-peta', compact('pengolahanPetaData'));
    }
}