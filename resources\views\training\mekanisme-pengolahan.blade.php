@extends('layouts.app')

@section('title', $mekanismeData['title'])

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="page-title">{{ $mekanismeData['title'] }}</h1>
                    <p class="page-subtitle">{{ $mekanismeData['subtitle'] }}</p>
                    <p class="page-description">{{ $mekanismeData['description'] }}</p>
                </div>
                <div class="col-lg-4 text-end">
                    <div class="material-info">
                        <div class="info-item">
                            <i class="fas fa-clock"></i>
                            <span>{{ $mekanismeData['duration'] }}</span>
                        </div>
                        <div class="info-item">
                            <i class="fas fa-signal"></i>
                            <span>{{ $mekanismeData['difficulty'] }}</span>
                        </div>
                        <a href="{{ $mekanismeData['pdf_link'] }}" class="btn btn-primary" target="_blank">
                            <i class="fas fa-download"></i> Download PDF
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Breadcrumb -->
    <div class="breadcrumb-section">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('home') }}">Beranda</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('materials') }}">Materi</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ $mekanismeData['title'] }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Content Sections -->
    <div class="content-sections">
        <div class="container">
            <div class="row">
                <!-- Main Content -->
                <div class="col-lg-9">
                    @foreach($mekanismeData['sections'] as $sectionKey => $section)
                    <div class="content-section" id="{{ $sectionKey }}">
                        <div class="section-header">
                            <h2 class="section-title">
                                <i class="{{ $section['icon'] }}"></i>
                                {{ $section['title'] }}
                            </h2>
                        </div>
                        
                        <div class="section-content">
                            @if(isset($section['content']))
                                @foreach($section['content'] as $content)
                                    <p class="section-text">{{ $content }}</p>
                                @endforeach
                            @endif

                            @if(isset($section['tahapan']))
                                <div class="tahapan-grid">
                                    @foreach($section['tahapan'] as $tahapanKey => $tahapan)
                                        <div class="tahapan-card">
                                            <div class="tahapan-header">
                                                <i class="{{ $tahapan['icon'] }}"></i>
                                                <h4>{{ $tahapan['title'] }}</h4>
                                            </div>
                                            <ul class="tahapan-list">
                                                @foreach($tahapan['items'] as $item)
                                                    <li>{{ $item }}</li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    @endforeach
                                </div>
                            @endif

                            @if(isset($section['pengolahan_master']))
                                <div class="subsection">
                                    <h3 class="subsection-title">
                                        <i class="{{ $section['pengolahan_master']['icon'] }}"></i>
                                        {{ $section['pengolahan_master']['title'] }}
                                    </h3>
                                    @foreach($section['pengolahan_master']['content'] as $content)
                                        <p>{{ $content }}</p>
                                    @endforeach
                                    <div class="komponen-list">
                                        <h4>Komponen Master Wilkerstat:</h4>
                                        <ul>
                                            @foreach($section['pengolahan_master']['komponen'] as $komponen)
                                                <li>{{ $komponen }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            @endif

                            @if(isset($section['muatan_sls']))
                                <div class="subsection">
                                    <h3 class="subsection-title">
                                        <i class="{{ $section['muatan_sls']['icon'] }}"></i>
                                        {{ $section['muatan_sls']['title'] }}
                                    </h3>
                                    @foreach($section['muatan_sls']['content'] as $content)
                                        <p>{{ $content }}</p>
                                    @endforeach
                                    <div class="komponen-list">
                                        <h4>Komponen Muatan SLS:</h4>
                                        <ul>
                                            @foreach($section['muatan_sls']['komponen'] as $komponen)
                                                <li>{{ $komponen }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            @endif

                            @if(isset($section['aplikasi_pendukung']))
                                <div class="subsection">
                                    <h3 class="subsection-title">
                                        <i class="{{ $section['aplikasi_pendukung']['icon'] }}"></i>
                                        {{ $section['aplikasi_pendukung']['title'] }}
                                    </h3>
                                    <div class="aplikasi-grid">
                                        <div class="aplikasi-card">
                                            <h4>{{ $section['aplikasi_pendukung']['frs_mfd']['name'] }}</h4>
                                            <p><strong>Fungsi:</strong> {{ $section['aplikasi_pendukung']['frs_mfd']['fungsi'] }}</p>
                                            <p><strong>Akses:</strong> {{ $section['aplikasi_pendukung']['frs_mfd']['url'] }}</p>
                                        </div>
                                        <div class="aplikasi-card">
                                            <h4>{{ $section['aplikasi_pendukung']['sipw']['name'] }}</h4>
                                            <p><strong>Fungsi:</strong> {{ $section['aplikasi_pendukung']['sipw']['fungsi'] }}</p>
                                            <p><strong>URL:</strong> <a href="{{ $section['aplikasi_pendukung']['sipw']['url'] }}" target="_blank">{{ $section['aplikasi_pendukung']['sipw']['url'] }}</a></p>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            @if(isset($section['tahapan_detail']))
                                <div class="alur-pengolahan">
                                    @foreach($section['tahapan_detail'] as $tahap)
                                        <div class="alur-step">
                                            <div class="step-number">{{ $tahap['step'] }}</div>
                                            <div class="step-content">
                                                <h4 class="step-title">{{ $tahap['title'] }}</h4>
                                                <p class="step-description">{{ $tahap['description'] }}</p>
                                                <div class="step-output">
                                                    <strong>Output:</strong> {{ $tahap['output'] }}
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @endif

                            @if(isset($section['prinsip']))
                                <div class="quality-control">
                                    <h4>Prinsip Kontrol Kualitas:</h4>
                                    <ul class="prinsip-list">
                                        @foreach($section['prinsip'] as $prinsip)
                                            <li>{{ $prinsip }}</li>
                                        @endforeach
                                    </ul>
                                    
                                    <h4>Tools dan Sistem:</h4>
                                    <ul class="tools-list">
                                        @foreach($section['tools'] as $tool)
                                            <li>{{ $tool }}</li>
                                        @endforeach
                                    </ul>
                                </div>
                            @endif

                            @if(isset($section['kesimpulan']))
                                <div class="section-conclusion">
                                    <div class="conclusion-header">
                                        <i class="fas fa-lightbulb"></i>
                                        <h4>Kesimpulan</h4>
                                    </div>
                                    <p class="conclusion-text">{{ $section['kesimpulan'] }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                    @endforeach
                </div>
                
                <!-- Sidebar -->
                <div class="col-lg-3">
                    <div class="sidebar">
                        <!-- Table of Contents -->
                        <div class="toc-card">
                            <h3 class="toc-title">
                                <i class="fas fa-list"></i>
                                Daftar Isi
                            </h3>
                            <ul class="toc-list">
                                @foreach($mekanismeData['sections'] as $sectionKey => $section)
                                    <li>
                                        <a href="#{{ $sectionKey }}" class="toc-link">
                                            <i class="{{ $section['icon'] }}"></i>
                                            {{ $section['title'] }}
                                        </a>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                        
                        <!-- Quick Actions -->
                        <div class="actions-card">
                            <h3 class="actions-title">
                                <i class="fas fa-bolt"></i>
                                Aksi Cepat
                            </h3>
                            <div class="actions-list">
                                <a href="{{ route('materials') }}" class="action-btn">
                                    <i class="fas fa-arrow-left"></i>
                                    Kembali ke Materi
                                </a>
                                <a href="{{ route('schedule') }}" class="action-btn">
                                    <i class="fas fa-calendar-alt"></i>
                                    Lihat Jadwal
                                </a>
                            </div>
                        </div>
                        
                        <!-- Material Info -->
                        <div class="material-info-card">
                            <h4>Informasi Materi</h4>
                            <div class="info-list">
                                <div class="info-row">
                                    <span class="info-label">Durasi:</span>
                                    <span class="info-value">{{ $mekanismeData['duration'] }}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Tingkat:</span>
                                    <span class="info-value">{{ $mekanismeData['difficulty'] }}</span>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">Format:</span>
                                    <span class="info-value">PDF + Web</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    /* Page Header */
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 4rem 0;
        margin-bottom: 2rem;
    }

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .page-subtitle {
        font-size: 1.25rem;
        margin-bottom: 1rem;
        opacity: 0.9;
    }

    .page-description {
        font-size: 1rem;
        opacity: 0.8;
        line-height: 1.6;
    }

    .material-info {
        text-align: right;
    }

    .info-item {
        display: inline-block;
        margin: 0 1rem 1rem 0;
        padding: 0.5rem 1rem;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 0.5rem;
        font-size: 0.9rem;
    }

    .info-item i {
        margin-right: 0.5rem;
    }

    /* Breadcrumb */
    .breadcrumb-section {
        background: #f8f9fa;
        padding: 1rem 0;
        margin-bottom: 2rem;
    }

    .breadcrumb {
        background: none;
        margin: 0;
        padding: 0;
    }

    .breadcrumb-item a {
        color: #6c757d;
        text-decoration: none;
    }

    .breadcrumb-item a:hover {
        color: #495057;
    }

    /* Content Sections */
    .content-section {
        background: white;
        border-radius: 0.75rem;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .section-header {
        border-bottom: 2px solid #e9ecef;
        padding-bottom: 1rem;
        margin-bottom: 2rem;
    }

    .section-title {
        font-size: 1.75rem;
        font-weight: 600;
        color: #495057;
        display: flex;
        align-items: center;
        gap: 0.75rem;
    }

    .section-title i {
        color: #667eea;
    }

    .section-text {
        font-size: 1rem;
        line-height: 1.7;
        color: #6c757d;
        margin-bottom: 1rem;
    }

    /* Tahapan Grid */
    .tahapan-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin: 2rem 0;
    }

    .tahapan-card {
        background: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1.5rem;
        border-left: 4px solid #667eea;
    }

    .tahapan-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;
    }

    .tahapan-header i {
        color: #667eea;
        font-size: 1.25rem;
    }

    .tahapan-header h4 {
        margin: 0;
        color: #495057;
    }

    .tahapan-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .tahapan-list li {
        padding: 0.5rem 0;
        border-bottom: 1px solid #e9ecef;
        position: relative;
        padding-left: 1.5rem;
    }

    .tahapan-list li:before {
        content: '✓';
        position: absolute;
        left: 0;
        color: #28a745;
        font-weight: bold;
    }

    .tahapan-list li:last-child {
        border-bottom: none;
    }

    /* Subsections */
    .subsection {
        margin: 2rem 0;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 0.5rem;
    }

    .subsection-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #495057;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .subsection-title i {
        color: #667eea;
    }

    .komponen-list h4 {
        color: #495057;
        margin-top: 1rem;
        margin-bottom: 0.5rem;
    }

    .komponen-list ul {
        list-style-type: disc;
        padding-left: 1.5rem;
    }

    .komponen-list li {
        margin-bottom: 0.5rem;
        color: #6c757d;
    }

    /* Aplikasi Grid */
    .aplikasi-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
    }

    .aplikasi-card {
        background: white;
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #e9ecef;
    }

    .aplikasi-card h4 {
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .aplikasi-card p {
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .aplikasi-card a {
        color: #667eea;
        text-decoration: none;
    }

    .aplikasi-card a:hover {
        text-decoration: underline;
    }

    /* Alur Pengolahan */
    .alur-pengolahan {
        margin: 2rem 0;
    }

    .alur-step {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        margin-bottom: 2rem;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 0.5rem;
        position: relative;
    }

    .alur-step:not(:last-child):after {
        content: '';
        position: absolute;
        left: 1.75rem;
        top: 100%;
        width: 2px;
        height: 1rem;
        background: #667eea;
    }

    .step-number {
        width: 3rem;
        height: 3rem;
        background: #667eea;
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 1.25rem;
        flex-shrink: 0;
    }

    .step-content {
        flex: 1;
    }

    .step-title {
        color: #495057;
        margin-bottom: 0.5rem;
    }

    .step-description {
        color: #6c757d;
        margin-bottom: 0.5rem;
    }

    .step-output {
        background: white;
        padding: 0.5rem;
        border-radius: 0.25rem;
        border-left: 3px solid #28a745;
        font-size: 0.9rem;
    }

    /* Quality Control */
    .quality-control h4 {
        color: #495057;
        margin-top: 1.5rem;
        margin-bottom: 0.75rem;
    }

    .prinsip-list, .tools-list {
        list-style-type: none;
        padding: 0;
    }

    .prinsip-list li, .tools-list li {
        padding: 0.5rem 0;
        padding-left: 1.5rem;
        position: relative;
        color: #6c757d;
        border-bottom: 1px solid #e9ecef;
    }

    .prinsip-list li:before {
        content: '🛡️';
        position: absolute;
        left: 0;
    }

    .tools-list li:before {
        content: '🔧';
        position: absolute;
        left: 0;
    }

    /* Section Conclusion */
    .section-conclusion {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 0.5rem;
        margin-top: 2rem;
    }

    .conclusion-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .conclusion-header h4 {
        margin: 0;
        color: white;
    }

    .conclusion-text {
        margin: 0;
        line-height: 1.6;
        opacity: 0.95;
    }

    /* Sidebar */
    .sidebar {
        position: sticky;
        top: 2rem;
    }

    .toc-card, .actions-card, .material-info-card {
        background: white;
        border-radius: 0.75rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .toc-title, .actions-title {
        font-size: 1.125rem;
        font-weight: 600;
        color: #495057;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
    }

    .toc-title i, .actions-title i {
        color: #667eea;
    }

    .toc-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .toc-link {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 0;
        color: #6c757d;
        text-decoration: none;
        border-bottom: 1px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .toc-link:hover {
        color: #667eea;
        padding-left: 0.5rem;
    }

    .toc-link:last-child {
        border-bottom: none;
    }

    .action-btn {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        background: #f8f9fa;
        color: #495057;
        text-decoration: none;
        border-radius: 0.5rem;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
    }

    .action-btn:hover {
        background: #667eea;
        color: white;
        transform: translateX(0.25rem);
    }

    .material-info-card h4 {
        color: #495057;
        margin-bottom: 1rem;
    }

    .info-row {
        display: flex;
        justify-content: space-between;
        padding: 0.5rem 0;
        border-bottom: 1px solid #e9ecef;
    }

    .info-row:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 500;
        color: #6c757d;
    }

    .info-value {
        color: #495057;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .page-header {
            padding: 2rem 0;
        }

        .page-title {
            font-size: 2rem;
        }

        .material-info {
            text-align: left;
            margin-top: 1rem;
        }

        .info-item {
            display: block;
            margin: 0.5rem 0;
        }

        .tahapan-grid {
            grid-template-columns: 1fr;
        }

        .aplikasi-grid {
            grid-template-columns: 1fr;
        }

        .alur-step {
            flex-direction: column;
            text-align: center;
        }

        .alur-step:not(:last-child):after {
            display: none;
        }

        .step-number {
            align-self: center;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Smooth scrolling for TOC links
        document.querySelectorAll('.toc-link').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Highlight active section in TOC
        const sections = document.querySelectorAll('.content-section');
        const tocLinks = document.querySelectorAll('.toc-link');

        function highlightActiveSection() {
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (window.pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            tocLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href').substring(1) === current) {
                    link.classList.add('active');
                }
            });
        }

        window.addEventListener('scroll', highlightActiveSection);
        highlightActiveSection(); // Initial call
    });
</script>
@endpush