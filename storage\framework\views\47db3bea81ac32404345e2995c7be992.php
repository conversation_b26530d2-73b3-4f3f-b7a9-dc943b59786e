<?php $__env->startSection('title', $sipwData['title']); ?>

<?php $__env->startSection('head'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/training-modern.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Modern Page Header -->
    <div class="modern-page-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8">
                    <h1 class="modern-page-title"><?php echo e($sipwData['title']); ?></h1>
                    <p class="modern-page-subtitle"><?php echo e($sipwData['subtitle']); ?></p>
                    <p class="modern-page-description"><?php echo e($sipwData['description']); ?></p>
                </div>
                <div class="col-lg-4">
                    <div class="modern-material-info mt-4">
                        
                        <a href="<?php echo e($sipwData['pdf_link']); ?>" class="modern-download-btn" target="_blank">
                            <i class="fas fa-download"></i>
                            Download PDF
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modern Breadcrumb -->
    <div class="modern-breadcrumb-section">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="modern-breadcrumb">
                    <li class="modern-breadcrumb-item">
                        <a href="<?php echo e(route('home')); ?>" class="modern-breadcrumb-link">Beranda</a>
                    </li>
                    <li class="modern-breadcrumb-item">
                        <a href="<?php echo e(route('materials')); ?>" class="modern-breadcrumb-link">Materi</a>
                    </li>
                    <li class="modern-breadcrumb-item">
                        <span class="modern-breadcrumb-current"><?php echo e($sipwData['title']); ?></span>
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Modern Content Layout -->
    <div class="modern-content-layout">
        <div class="container">
            <div class="modern-content-grid">
                <!-- Main Content -->
                <div class="main-content">
                    <div class="modern-accordion">
                        <?php $__currentLoopData = $sipwData['sections']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sectionKey => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="modern-accordion-item" id="<?php echo e($sectionKey); ?>">
                            <button class="modern-accordion-header">
                                <div class="modern-accordion-title">
                                    <i class="modern-accordion-icon <?php echo e($section['icon']); ?>"></i>
                                    <?php echo e($section['title']); ?>

                                </div>
                                <i class="modern-accordion-chevron fas fa-chevron-down"></i>
                            </button>

                            <div class="modern-accordion-content">
                                <div class="modern-accordion-body">
                                    <?php if($sectionKey === 'penjelasan_umum'): ?>
                                        <div class="modern-feature-grid">
                                            <?php $__currentLoopData = $section['content']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contentKey => $contentItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="modern-feature-card">
                                                <div class="modern-feature-icon">
                                                    <i class="fas fa-info-circle"></i>
                                                </div>
                                                <h5 class="modern-feature-title"><?php echo e($contentItem['title']); ?></h5>
                                                <ul class="modern-list">
                                                    <?php $__currentLoopData = $contentItem['items']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <li class="modern-list-item">
                                                        <div class="modern-list-icon">
                                                            <i class="fas fa-check"></i>
                                                        </div>
                                                        <div class="modern-list-content"><?php echo e($item); ?></div>
                                                    </li>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </ul>
                                            </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php elseif($sectionKey === 'tata_cara_pengoperasian'): ?>
                                        <div class="mb-6">
                                            <?php $__currentLoopData = $section['content']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contentKey => $contentItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="modern-feature-card mb-6">
                                                <div class="modern-feature-icon">
                                                    <i class="fas fa-cogs"></i>
                                                </div>
                                                <h5 class="modern-feature-title"><?php echo e($contentItem['title']); ?></h5>
                                                <div class="modern-feature-description">
                                                    <?php if(isset($contentItem['description'])): ?>
                                                        <p class="mb-4"><?php echo e($contentItem['description']); ?></p>
                                                    <?php endif; ?>

                                                    <?php if(isset($contentItem['steps'])): ?>
                                                        <div class="mb-4">
                                                            <h6 class="font-semibold mb-3">Langkah-langkah:</h6>
                                                            <div class="workflow-container">
                                                                <?php $__currentLoopData = $contentItem['steps']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $step): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <div class="workflow-step">
                                                                    <div class="workflow-step-header">
                                                                        <div class="workflow-step-number"><?php echo e($index + 1); ?></div>
                                                                        <h6 class="workflow-step-title">Langkah <?php echo e($index + 1); ?></h6>
                                                                    </div>
                                                                    <div class="workflow-step-content">
                                                                        <p><?php echo e($step); ?></p>
                                                                    </div>
                                                                    <?php if(!$loop->last): ?>
                                                                    <div class="workflow-connector">
                                                                        <i class="fas fa-arrow-down"></i>
                                                                    </div>
                                                                    <?php endif; ?>
                                                                </div>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>

                                                    <?php if(isset($contentItem['features'])): ?>
                                                        <div class="mb-4">
                                                            <h6 class="font-semibold mb-3">Fitur:</h6>
                                                            <ul class="modern-list">
                                                                <?php $__currentLoopData = $contentItem['features']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <li class="modern-list-item">
                                                                    <div class="modern-list-icon">
                                                                        <i class="fas fa-star"></i>
                                                                    </div>
                                                                    <div class="modern-list-content"><?php echo e($feature); ?></div>
                                                                </li>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </ul>
                                                        </div>
                                                    <?php endif; ?>

                                                    <?php if(isset($contentItem['roles'])): ?>
                                                        <div class="mb-4">
                                                            <h6 class="font-semibold mb-3">Role Pengguna:</h6>
                                                            <ul class="modern-list">
                                                                <?php $__currentLoopData = $contentItem['roles']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <li class="modern-list-item">
                                                                    <div class="modern-list-icon">
                                                                        <i class="fas fa-user-tag"></i>
                                                                    </div>
                                                                    <div class="modern-list-content"><?php echo e($role); ?></div>
                                                                </li>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </ul>
                                                        </div>
                                                    <?php endif; ?>

                                                    <?php if(isset($contentItem['process'])): ?>
                                                        <div class="mb-4">
                                                            <h6 class="font-semibold mb-3">Proses:</h6>
                                                            <ul class="modern-list">
                                                                <?php $__currentLoopData = $contentItem['process']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $process): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <li class="modern-list-item">
                                                                    <div class="modern-list-icon">
                                                                        <i class="fas fa-arrow-right"></i>
                                                                    </div>
                                                                    <div class="modern-list-content"><?php echo e($process); ?></div>
                                                                </li>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </ul>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php elseif($sectionKey === 'tata_cara_entri'): ?>
                                        <div class="mb-6">
                                            <?php $__currentLoopData = $section['content']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contentKey => $contentItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="modern-feature-card mb-6">
                                                <div class="modern-feature-icon">
                                                    <i class="fas fa-keyboard"></i>
                                                </div>
                                                <h5 class="modern-feature-title"><?php echo e($contentItem['title']); ?></h5>
                                                <div class="modern-feature-description">
                                                    <p class="mb-4"><?php echo e($contentItem['description']); ?></p>

                                                    <?php if(isset($contentItem['steps'])): ?>
                                                        <div class="mb-4">
                                                            <h6 class="font-semibold mb-3">Langkah-langkah:</h6>
                                                            <div class="workflow-container">
                                                                <?php $__currentLoopData = $contentItem['steps']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $step): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <div class="workflow-step">
                                                                    <div class="workflow-step-header">
                                                                        <div class="workflow-step-number"><?php echo e($index + 1); ?></div>
                                                                        <h6 class="workflow-step-title">Langkah <?php echo e($index + 1); ?></h6>
                                                                    </div>
                                                                    <div class="workflow-step-content">
                                                                        <p><?php echo e($step); ?></p>
                                                                    </div>
                                                                    <?php if(!$loop->last): ?>
                                                                    <div class="workflow-connector">
                                                                        <i class="fas fa-arrow-down"></i>
                                                                    </div>
                                                                    <?php endif; ?>
                                                                </div>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php elseif($sectionKey === 'catatan_tambahan'): ?>
                                        <div class="mb-6">
                                            <?php $__currentLoopData = $section['content']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contentKey => $contentItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="modern-alert modern-alert-warning mb-4">
                                                <div class="modern-alert-icon">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                </div>
                                                <div class="modern-alert-content">
                                                    <h6 class="font-semibold mb-2"><?php echo e($contentItem['title']); ?></h6>
                                                    <p class="mb-0"><?php echo e($contentItem['description']); ?></p>
                                                </div>
                                            </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    <?php endif; ?>

                                    <?php if(isset($section['kesimpulan'])): ?>
                                    <div class="modern-alert modern-alert-success">
                                        <div class="modern-alert-icon">
                                            <i class="fas fa-lightbulb"></i>
                                        </div>
                                        <div class="modern-alert-content">
                                            <h6 class="font-semibold mb-2">Kesimpulan</h6>
                                            <p class="mb-0"><?php echo e($section['kesimpulan']); ?></p>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>

                <!-- Modern Sidebar -->
                <div class="modern-sidebar">
                    <!-- Table of Contents -->
                    <div class="modern-sidebar-card">
                        <h4 class="modern-sidebar-title">Daftar Isi</h4>
                        <ul class="modern-toc">
                            <?php $__currentLoopData = $sipwData['sections']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sectionKey => $section): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li class="modern-toc-item">
                                <a href="#<?php echo e($sectionKey); ?>" class="modern-toc-link">
                                    <i class="modern-toc-icon <?php echo e($section['icon']); ?>"></i>
                                    <?php echo e($section['title']); ?>

                                </a>
                            </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>

                    <!-- Quick Actions -->
                    <div class="modern-sidebar-card">
                        <h4 class="modern-sidebar-title">Aksi Cepat</h4>
                        <div class="modern-action-buttons">
                            <a href="<?php echo e($sipwData['pdf_link']); ?>" class="modern-btn modern-btn-primary" target="_blank">
                                <i class="fas fa-download"></i>
                                Download PDF
                            </a>
                            <a href="<?php echo e(route('materials')); ?>" class="modern-btn modern-btn-secondary">
                                <i class="fas fa-arrow-left"></i>
                                Kembali ke Materi
                            </a>
                            <a href="<?php echo e(route('schedule')); ?>" class="modern-btn modern-btn-outline">
                                <i class="fas fa-calendar"></i>
                                Lihat Jadwal
                            </a>
                        </div>
                    </div>

                    <!-- Progress Card -->
                    <div class="modern-sidebar-card">
                        <h4 class="modern-sidebar-title">Progress Pembelajaran</h4>
                        <div class="modern-progress-card">
                            <div class="modern-progress-header">
                                <div class="modern-progress-number">4</div>
                                <h5 class="modern-progress-title">SIPW SE2026</h5>
                            </div>
                            <div class="modern-progress-content">
                                Memahami sistem pemutakhiran wilkerstat dan cara pengoperasiannya.
                            </div>
                        </div>
                    </div>

                    <!-- System Features -->
                    <div class="modern-sidebar-card">
                        <h4 class="modern-sidebar-title">Fitur Sistem</h4>
                        <div class="system-features">
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-database"></i>
                                </div>
                                <span>Data Management</span>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-sync"></i>
                                </div>
                                <span>Auto Sync</span>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <span>Security</span>
                            </div>
                            <div class="feature-item">
                                <div class="feature-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <span>Analytics</span>
                            </div>
                        </div>
                    </div>
                </div>
                                    <span>Kembali ke Materi</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="<?php echo e(asset('js/training-modern.js')); ?>"></script>
<script>
// Additional system-specific styles and functionality
document.addEventListener('DOMContentLoaded', function() {
    const systemStyles = `
        .system-features {
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
        }
        .feature-item {
            display: flex;
            align-items: center;
            gap: var(--space-3);
            padding: var(--space-3);
            background: var(--gray-50);
            border-radius: var(--radius-lg);
            transition: all var(--transition-fast);
        }
        .feature-item:hover {
            background: var(--primary-50);
            color: var(--primary-700);
        }
        .feature-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background: var(--primary-100);
            color: var(--primary-600);
            border-radius: var(--radius-lg);
            font-size: var(--text-sm);
        }
        .feature-item:hover .feature-icon {
            background: var(--primary-200);
            color: var(--primary-700);
        }
    `;

    const styleSheet = document.createElement('style');
    styleSheet.textContent = systemStyles;
    document.head.appendChild(styleSheet);
});
</script>
<?php $__env->stopSection(); ?>

    .page-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
    }

    .page-subtitle {
        font-size: 1.25rem;
        margin-bottom: 0.5rem;
        opacity: 0.9;
    }

    .page-description {
        font-size: 1rem;
        opacity: 0.8;
        line-height: 1.6;
    }

    .material-info {
        background: rgba(255, 255, 255, 0.1);
        padding: 1.5rem;
        border-radius: 12px;
        backdrop-filter: blur(10px);
    }

    .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
        font-size: 0.95rem;
    }

    .info-item i {
        margin-right: 0.5rem;
        width: 20px;
    }

    .breadcrumb-section {
        background: #f8f9fa;
        padding: 1rem 0;
        border-bottom: 1px solid #e9ecef;
    }

    .content-sections {
        padding: 3rem 0;
    }

    .content-section {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
    }

    .section-header {
        border-bottom: 2px solid #667eea;
        padding-bottom: 1rem;
        margin-bottom: 2rem;
    }

    .section-title {
        color: #2d3748;
        font-size: 1.75rem;
        font-weight: 600;
        margin: 0;
    }

    .section-title i {
        color: #667eea;
        margin-right: 0.75rem;
    }

    .subsection {
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #667eea;
    }

    .subsection-title {
        color: #2d3748;
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .subsection-description {
        color: #4a5568;
        line-height: 1.6;
        margin-bottom: 1rem;
    }

    .content-list, .steps-list, .features-list, .roles-list, .process-list {
        margin: 1rem 0;
        padding-left: 1.5rem;
    }

    .content-list li, .features-list li, .roles-list li, .process-list li {
        margin-bottom: 0.5rem;
        color: #4a5568;
        line-height: 1.6;
    }

    .steps-list {
        counter-reset: step-counter;
    }

    .steps-list li {
        counter-increment: step-counter;
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: white;
        border-radius: 6px;
        border-left: 3px solid #667eea;
        position: relative;
    }

    .steps-list li::before {
        content: counter(step-counter);
        position: absolute;
        left: -15px;
        top: 50%;
        transform: translateY(-50%);
        background: #667eea;
        color: white;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .section-conclusion {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 8px;
        margin-top: 2rem;
    }

    .conclusion-header {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .conclusion-header i {
        margin-right: 0.5rem;
        font-size: 1.2rem;
    }

    .conclusion-header h4 {
        margin: 0;
        font-weight: 600;
    }

    .conclusion-text {
        line-height: 1.6;
        margin: 0;
        opacity: 0.95;
    }

    .sidebar {
        position: sticky;
        top: 2rem;
    }

    .toc-card, .quick-actions-card, .related-materials-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border: 1px solid #e9ecef;
    }

    .toc-title, .card-title {
        color: #2d3748;
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
    }

    .toc-title i, .card-title i {
        margin-right: 0.5rem;
        color: #667eea;
    }

    .toc-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .toc-link {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        color: #4a5568;
        text-decoration: none;
        border-radius: 6px;
        transition: all 0.2s;
        margin-bottom: 0.25rem;
    }

    .toc-link:hover {
        background: #667eea;
        color: white;
        transform: translateX(5px);
    }

    .toc-link i {
        margin-right: 0.75rem;
        width: 16px;
    }

    .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .action-btn {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        color: #4a5568;
        text-decoration: none;
        transition: all 0.2s;
        cursor: pointer;
        font-size: 0.9rem;
    }

    .action-btn:hover {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }

    .action-btn i {
        margin-right: 0.5rem;
    }

    .related-list {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
    }

    .related-item {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        color: #4a5568;
        text-decoration: none;
        border-radius: 6px;
        transition: all 0.2s;
        border: 1px solid #e9ecef;
    }

    .related-item:hover {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }

    .related-item i {
        margin-right: 0.75rem;
        width: 16px;
    }

    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }
        
        .content-section {
            padding: 1.5rem;
        }
        
        .sidebar {
            position: static;
            margin-top: 2rem;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    function scrollToSection(sectionId) {
        const element = document.getElementById(sectionId);
        if (element) {
            element.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }

    function printPage() {
        window.print();
    }

    function shareContent() {
        if (navigator.share) {
            navigator.share({
                title: '<?php echo e($sipwData["title"]); ?>',
                text: '<?php echo e($sipwData["description"]); ?>',
                url: window.location.href
            });
        } else {
            // Fallback: copy URL to clipboard
            navigator.clipboard.writeText(window.location.href).then(() => {
                alert('Link telah disalin ke clipboard!');
            });
        }
    }

    // Highlight active section in TOC
    window.addEventListener('scroll', function() {
        const sections = document.querySelectorAll('.content-section');
        const tocLinks = document.querySelectorAll('.toc-link');
        
        let current = '';
        sections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionHeight = section.clientHeight;
            if (pageYOffset >= sectionTop - 200) {
                current = section.getAttribute('id');
            }
        });
        
        tocLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === '#' + current) {
                link.classList.add('active');
            }
        });
    });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\bps renata kerja\2024\project stat sektoral website\Taylor-Swift-Web-Project-main\pengolahanwilker\resources\views/training/sipw-se2026.blade.php ENDPATH**/ ?>